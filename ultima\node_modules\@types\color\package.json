{"name": "@types/color", "version": "4.2.0", "description": "TypeScript definitions for color", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/color", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "Airlun", "url": "https://github.com/Airlun"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jameswlane"}, {"name": "<PERSON>", "githubUsername": "Beee<PERSON><PERSON>ue", "url": "https://github.com/BeeeQueue"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/color"}, "scripts": {}, "dependencies": {"@types/color-convert": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "635e03ace5c53fb89421c29351f931aa51a4607745b331cb4964c69b5bfee52f", "typeScriptVersion": "4.8"}