declare namespace ColorName {
    /**
     * Tuple of Red, Green, and Blue
     * @example
     * // Red = 55, Green = 70, Blue = 0
     * const ColorName.RGB: ColorName.RGB = [55, 70, 0];
     */
    type RGB = [number, number, number];
}

declare const ColorName: {
    aliceblue: ColorName.RGB;
    antiquewhite: ColorName.RGB;
    aqua: ColorName.RGB;
    aquamarine: ColorName.RGB;
    azure: ColorName.RGB;
    beige: ColorName.RGB;
    bisque: ColorName.RGB;
    black: ColorName.RGB;
    blanchedalmond: ColorName.RGB;
    blue: ColorName.RGB;
    blueviolet: ColorName.RGB;
    brown: ColorName.RGB;
    burlywood: ColorName.RGB;
    cadetblue: ColorName.RGB;
    chartreuse: ColorName.RGB;
    chocolate: ColorName.RGB;
    coral: ColorName.RGB;
    cornflowerblue: ColorName.RGB;
    cornsilk: ColorName.RGB;
    crimson: ColorName.RGB;
    cyan: ColorName.RGB;
    darkblue: ColorName.RGB;
    darkcyan: ColorName.RGB;
    darkgoldenrod: ColorName.RGB;
    darkgray: ColorName.RGB;
    darkgreen: ColorName.RGB;
    darkgrey: ColorName.RGB;
    darkkhaki: ColorName.RGB;
    darkmagenta: ColorName.RGB;
    darkolivegreen: ColorName.RGB;
    darkorange: ColorName.RGB;
    darkorchid: ColorName.RGB;
    darkred: ColorName.RGB;
    darksalmon: ColorName.RGB;
    darkseagreen: ColorName.RGB;
    darkslateblue: ColorName.RGB;
    darkslategray: ColorName.RGB;
    darkslategrey: ColorName.RGB;
    darkturquoise: ColorName.RGB;
    darkviolet: ColorName.RGB;
    deeppink: ColorName.RGB;
    deepskyblue: ColorName.RGB;
    dimgray: ColorName.RGB;
    dimgrey: ColorName.RGB;
    dodgerblue: ColorName.RGB;
    firebrick: ColorName.RGB;
    floralwhite: ColorName.RGB;
    forestgreen: ColorName.RGB;
    fuchsia: ColorName.RGB;
    gainsboro: ColorName.RGB;
    ghostwhite: ColorName.RGB;
    gold: ColorName.RGB;
    goldenrod: ColorName.RGB;
    gray: ColorName.RGB;
    green: ColorName.RGB;
    greenyellow: ColorName.RGB;
    grey: ColorName.RGB;
    honeydew: ColorName.RGB;
    hotpink: ColorName.RGB;
    indianred: ColorName.RGB;
    indigo: ColorName.RGB;
    ivory: ColorName.RGB;
    khaki: ColorName.RGB;
    lavender: ColorName.RGB;
    lavenderblush: ColorName.RGB;
    lawngreen: ColorName.RGB;
    lemonchiffon: ColorName.RGB;
    lightblue: ColorName.RGB;
    lightcoral: ColorName.RGB;
    lightcyan: ColorName.RGB;
    lightgoldenrodyellow: ColorName.RGB;
    lightgray: ColorName.RGB;
    lightgreen: ColorName.RGB;
    lightgrey: ColorName.RGB;
    lightpink: ColorName.RGB;
    lightsalmon: ColorName.RGB;
    lightseagreen: ColorName.RGB;
    lightskyblue: ColorName.RGB;
    lightslategray: ColorName.RGB;
    lightslategrey: ColorName.RGB;
    lightsteelblue: ColorName.RGB;
    lightyellow: ColorName.RGB;
    lime: ColorName.RGB;
    limegreen: ColorName.RGB;
    linen: ColorName.RGB;
    magenta: ColorName.RGB;
    maroon: ColorName.RGB;
    mediumaquamarine: ColorName.RGB;
    mediumblue: ColorName.RGB;
    mediumorchid: ColorName.RGB;
    mediumpurple: ColorName.RGB;
    mediumseagreen: ColorName.RGB;
    mediumslateblue: ColorName.RGB;
    mediumspringgreen: ColorName.RGB;
    mediumturquoise: ColorName.RGB;
    mediumvioletred: ColorName.RGB;
    midnightblue: ColorName.RGB;
    mintcream: ColorName.RGB;
    mistyrose: ColorName.RGB;
    moccasin: ColorName.RGB;
    navajowhite: ColorName.RGB;
    navy: ColorName.RGB;
    oldlace: ColorName.RGB;
    olive: ColorName.RGB;
    olivedrab: ColorName.RGB;
    orange: ColorName.RGB;
    orangered: ColorName.RGB;
    orchid: ColorName.RGB;
    palegoldenrod: ColorName.RGB;
    palegreen: ColorName.RGB;
    paleturquoise: ColorName.RGB;
    palevioletred: ColorName.RGB;
    papayawhip: ColorName.RGB;
    peachpuff: ColorName.RGB;
    peru: ColorName.RGB;
    pink: ColorName.RGB;
    plum: ColorName.RGB;
    powderblue: ColorName.RGB;
    purple: ColorName.RGB;
    rebeccapurple: ColorName.RGB;
    red: ColorName.RGB;
    rosybrown: ColorName.RGB;
    royalblue: ColorName.RGB;
    saddlebrown: ColorName.RGB;
    salmon: ColorName.RGB;
    sandybrown: ColorName.RGB;
    seagreen: ColorName.RGB;
    seashell: ColorName.RGB;
    sienna: ColorName.RGB;
    silver: ColorName.RGB;
    skyblue: ColorName.RGB;
    slateblue: ColorName.RGB;
    slategray: ColorName.RGB;
    slategrey: ColorName.RGB;
    snow: ColorName.RGB;
    springgreen: ColorName.RGB;
    steelblue: ColorName.RGB;
    tan: ColorName.RGB;
    teal: ColorName.RGB;
    thistle: ColorName.RGB;
    tomato: ColorName.RGB;
    turquoise: ColorName.RGB;
    violet: ColorName.RGB;
    wheat: ColorName.RGB;
    white: ColorName.RGB;
    whitesmoke: ColorName.RGB;
    yellow: ColorName.RGB;
    yellowgreen: ColorName.RGB;
};

export = ColorName;
