{"version": 3, "file": "index.esmodule.js", "sources": ["../src/hooks/useEventCallback.ts", "../src/utils/clamp.ts", "../src/components/common/Interactive.tsx", "../src/utils/format.ts", "../src/components/common/Pointer.tsx", "../src/utils/round.ts", "../src/utils/convert.ts", "../src/components/common/Hue.tsx", "../src/components/common/Saturation.tsx", "../src/utils/compare.ts", "../src/hooks/useColorManipulation.ts", "../src/hooks/useIsomorphicLayoutEffect.ts", "../src/utils/nonce.ts", "../src/hooks/useStyleSheet.ts", "../src/components/common/ColorPicker.tsx", "../src/components/HexColorPicker.tsx", "../src/components/common/Alpha.tsx", "../src/components/common/AlphaColorPicker.tsx", "../src/components/HexAlphaColorPicker.tsx", "../src/components/HslaColorPicker.tsx", "../src/components/HslaStringColorPicker.tsx", "../src/components/HslColorPicker.tsx", "../src/components/HslStringColorPicker.tsx", "../src/components/HsvaColorPicker.tsx", "../src/components/HsvaStringColorPicker.tsx", "../src/components/HsvColorPicker.tsx", "../src/components/HsvStringColorPicker.tsx", "../src/components/RgbaColorPicker.tsx", "../src/components/RgbaStringColorPicker.tsx", "../src/components/RgbColorPicker.tsx", "../src/components/RgbStringColorPicker.tsx", "../src/utils/validate.ts", "../src/components/common/ColorInput.tsx", "../src/components/HexColorInput.tsx"], "sourcesContent": ["import { useRef } from \"react\";\n\n// Saves incoming handler to the ref in order to avoid \"useCallback hell\"\nexport function useEventCallback<T>(handler?: (value: T) => void): (value: T) => void {\n  const callbackRef = useRef(handler);\n  const fn = useRef((value: T) => {\n    callbackRef.current && callbackRef.current(value);\n  });\n  callbackRef.current = handler;\n\n  return fn.current;\n}\n", "// Clamps a value between an upper and lower bound.\n// We use ternary operators because it makes the minified code\n// 2 times shorter then `Math.min(Math.max(a,b),c)`\nexport const clamp = (number: number, min = 0, max = 1): number => {\n  return number > max ? max : number < min ? min : number;\n};\n", "import React, { useRef, useMemo, useEffect } from \"react\";\n\nimport { useEventCallback } from \"../../hooks/useEventCallback\";\nimport { clamp } from \"../../utils/clamp\";\n\nexport interface Interaction {\n  left: number;\n  top: number;\n}\n\n// Check if an event was triggered by touch\nconst isTouch = (event: MouseEvent | TouchEvent): event is TouchEvent => \"touches\" in event;\n\n// Finds a proper touch point by its identifier\nconst getTouchPoint = (touches: TouchList, touchId: null | number): Touch => {\n  for (let i = 0; i < touches.length; i++) {\n    if (touches[i].identifier === touchId) return touches[i];\n  }\n  return touches[0];\n};\n\n// Finds the proper window object to fix iframe embedding issues\nconst getParentWindow = (node?: HTMLDivElement | null): Window => {\n  return (node && node.ownerDocument.defaultView) || self;\n};\n\n// Returns a relative position of the pointer inside the node's bounding box\nconst getRelativePosition = (\n  node: HTMLDivElement,\n  event: MouseEvent | TouchEvent,\n  touchId: null | number\n): Interaction => {\n  const rect = node.getBoundingClientRect();\n\n  // Get user's pointer position from `touches` array if it's a `TouchEvent`\n  const pointer = isTouch(event) ? getTouchPoint(event.touches, touchId) : (event as MouseEvent);\n\n  return {\n    left: clamp((pointer.pageX - (rect.left + getParentWindow(node).pageXOffset)) / rect.width),\n    top: clamp((pointer.pageY - (rect.top + getParentWindow(node).pageYOffset)) / rect.height),\n  };\n};\n\n// Browsers introduced an intervention, making touch events passive by default.\n// This workaround removes `preventDefault` call from the touch handlers.\n// https://github.com/facebook/react/issues/19651\nconst preventDefaultMove = (event: MouseEvent | TouchEvent): void => {\n  !isTouch(event) && event.preventDefault();\n};\n\n// Prevent mobile browsers from handling mouse events (conflicting with touch ones).\n// If we detected a touch interaction before, we prefer reacting to touch events only.\nconst isInvalid = (event: MouseEvent | TouchEvent, hasTouch: boolean): boolean => {\n  return hasTouch && !isTouch(event);\n};\n\ninterface Props {\n  onMove: (interaction: Interaction) => void;\n  onKey: (offset: Interaction) => void;\n  children: React.ReactNode;\n}\n\nconst InteractiveBase = ({ onMove, onKey, ...rest }: Props) => {\n  const container = useRef<HTMLDivElement>(null);\n  const onMoveCallback = useEventCallback<Interaction>(onMove);\n  const onKeyCallback = useEventCallback<Interaction>(onKey);\n  const touchId = useRef<null | number>(null);\n  const hasTouch = useRef(false);\n\n  const [handleMoveStart, handleKeyDown, toggleDocumentEvents] = useMemo(() => {\n    const handleMoveStart = ({ nativeEvent }: React.MouseEvent | React.TouchEvent) => {\n      const el = container.current;\n      if (!el) return;\n\n      // Prevent text selection\n      preventDefaultMove(nativeEvent);\n\n      if (isInvalid(nativeEvent, hasTouch.current) || !el) return;\n\n      if (isTouch(nativeEvent)) {\n        hasTouch.current = true;\n        const changedTouches = nativeEvent.changedTouches || [];\n        if (changedTouches.length) touchId.current = changedTouches[0].identifier;\n      }\n\n      el.focus();\n      onMoveCallback(getRelativePosition(el, nativeEvent, touchId.current));\n      toggleDocumentEvents(true);\n    };\n\n    const handleMove = (event: MouseEvent | TouchEvent) => {\n      // Prevent text selection\n      preventDefaultMove(event);\n\n      // If user moves the pointer outside of the window or iframe bounds and release it there,\n      // `mouseup`/`touchend` won't be fired. In order to stop the picker from following the cursor\n      // after the user has moved the mouse/finger back to the document, we check `event.buttons`\n      // and `event.touches`. It allows us to detect that the user is just moving his pointer\n      // without pressing it down\n      const isDown = isTouch(event) ? event.touches.length > 0 : event.buttons > 0;\n\n      if (isDown && container.current) {\n        onMoveCallback(getRelativePosition(container.current, event, touchId.current));\n      } else {\n        toggleDocumentEvents(false);\n      }\n    };\n\n    const handleMoveEnd = () => toggleDocumentEvents(false);\n\n    const handleKeyDown = (event: React.KeyboardEvent) => {\n      const keyCode = event.which || event.keyCode;\n\n      // Ignore all keys except arrow ones\n      if (keyCode < 37 || keyCode > 40) return;\n      // Do not scroll page by arrow keys when document is focused on the element\n      event.preventDefault();\n      // Send relative offset to the parent component.\n      // We use codes (37←, 38↑, 39→, 40↓) instead of keys ('ArrowRight', 'ArrowDown', etc)\n      // to reduce the size of the library\n      onKeyCallback({\n        left: keyCode === 39 ? 0.05 : keyCode === 37 ? -0.05 : 0,\n        top: keyCode === 40 ? 0.05 : keyCode === 38 ? -0.05 : 0,\n      });\n    };\n\n    function toggleDocumentEvents(state?: boolean) {\n      const touch = hasTouch.current;\n      const el = container.current;\n      const parentWindow = getParentWindow(el);\n\n      // Add or remove additional pointer event listeners\n      const toggleEvent = state ? parentWindow.addEventListener : parentWindow.removeEventListener;\n      toggleEvent(touch ? \"touchmove\" : \"mousemove\", handleMove);\n      toggleEvent(touch ? \"touchend\" : \"mouseup\", handleMoveEnd);\n    }\n\n    return [handleMoveStart, handleKeyDown, toggleDocumentEvents];\n  }, [onKeyCallback, onMoveCallback]);\n\n  // Remove window event listeners before unmounting\n  useEffect(() => toggleDocumentEvents, [toggleDocumentEvents]);\n\n  return (\n    <div\n      {...rest}\n      onTouchStart={handleMoveStart}\n      onMouseDown={handleMoveStart}\n      className=\"react-colorful__interactive\"\n      ref={container}\n      onKeyDown={handleKeyDown}\n      tabIndex={0}\n      role=\"slider\"\n    />\n  );\n};\n\nexport const Interactive = React.memo(InteractiveBase);\n", "export const formatClassName = (names: unknown[]): string => names.filter(Boolean).join(\" \");\n", "import React from \"react\";\nimport { formatClassName } from \"../../utils/format\";\n\ninterface Props {\n  className?: string;\n  top?: number;\n  left: number;\n  color: string;\n}\n\nexport const Pointer = ({ className, color, left, top = 0.5 }: Props): JSX.Element => {\n  const nodeClassName = formatClassName([\"react-colorful__pointer\", className]);\n\n  const style = {\n    top: `${top * 100}%`,\n    left: `${left * 100}%`,\n  };\n\n  return (\n    <div className={nodeClassName} style={style}>\n      <div className=\"react-colorful__pointer-fill\" style={{ backgroundColor: color }} />\n    </div>\n  );\n};\n", "export const round = (number: number, digits = 0, base = Math.pow(10, digits)): number => {\n  return Math.round(base * number) / base;\n};\n", "import { round } from \"./round\";\nimport { RgbaColor, RgbColor, HslaColor, HslColor, HsvaColor, HsvColor } from \"../types\";\n\n/**\n * Valid CSS <angle> units.\n * https://developer.mozilla.org/en-US/docs/Web/CSS/angle\n */\nconst angleUnits: Record<string, number> = {\n  grad: 360 / 400,\n  turn: 360,\n  rad: 360 / (Math.PI * 2),\n};\n\nexport const hexToHsva = (hex: string): HsvaColor => rgbaToHsva(hexToRgba(hex));\n\nexport const hexToRgba = (hex: string): RgbaColor => {\n  if (hex[0] === \"#\") hex = hex.substring(1);\n\n  if (hex.length < 6) {\n    return {\n      r: parseInt(hex[0] + hex[0], 16),\n      g: parseInt(hex[1] + hex[1], 16),\n      b: parseInt(hex[2] + hex[2], 16),\n      a: hex.length === 4 ? round(parseInt(hex[3] + hex[3], 16) / 255, 2) : 1,\n    };\n  }\n\n  return {\n    r: parseInt(hex.substring(0, 2), 16),\n    g: parseInt(hex.substring(2, 4), 16),\n    b: parseInt(hex.substring(4, 6), 16),\n    a: hex.length === 8 ? round(parseInt(hex.substring(6, 8), 16) / 255, 2) : 1,\n  };\n};\n\nexport const parseHue = (value: string, unit = \"deg\"): number => {\n  return Number(value) * (angleUnits[unit] || 1);\n};\n\nexport const hslaStringToHsva = (hslString: string): HsvaColor => {\n  const matcher = /hsla?\\(?\\s*(-?\\d*\\.?\\d+)(deg|rad|grad|turn)?[,\\s]+(-?\\d*\\.?\\d+)%?[,\\s]+(-?\\d*\\.?\\d+)%?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i;\n  const match = matcher.exec(hslString);\n\n  if (!match) return { h: 0, s: 0, v: 0, a: 1 };\n\n  return hslaToHsva({\n    h: parseHue(match[1], match[2]),\n    s: Number(match[3]),\n    l: Number(match[4]),\n    a: match[5] === undefined ? 1 : Number(match[5]) / (match[6] ? 100 : 1),\n  });\n};\n\nexport const hslStringToHsva = hslaStringToHsva;\n\nexport const hslaToHsva = ({ h, s, l, a }: HslaColor): HsvaColor => {\n  s *= (l < 50 ? l : 100 - l) / 100;\n\n  return {\n    h: h,\n    s: s > 0 ? ((2 * s) / (l + s)) * 100 : 0,\n    v: l + s,\n    a,\n  };\n};\n\nexport const hsvaToHex = (hsva: HsvaColor): string => rgbaToHex(hsvaToRgba(hsva));\n\nexport const hsvaToHsla = ({ h, s, v, a }: HsvaColor): HslaColor => {\n  const hh = ((200 - s) * v) / 100;\n\n  return {\n    h: round(h),\n    s: round(hh > 0 && hh < 200 ? ((s * v) / 100 / (hh <= 100 ? hh : 200 - hh)) * 100 : 0),\n    l: round(hh / 2),\n    a: round(a, 2),\n  };\n};\n\nexport const hsvaToHslString = (hsva: HsvaColor): string => {\n  const { h, s, l } = hsvaToHsla(hsva);\n  return `hsl(${h}, ${s}%, ${l}%)`;\n};\n\nexport const hsvaToHsvString = (hsva: HsvaColor): string => {\n  const { h, s, v } = roundHsva(hsva);\n  return `hsv(${h}, ${s}%, ${v}%)`;\n};\n\nexport const hsvaToHsvaString = (hsva: HsvaColor): string => {\n  const { h, s, v, a } = roundHsva(hsva);\n  return `hsva(${h}, ${s}%, ${v}%, ${a})`;\n};\n\nexport const hsvaToHslaString = (hsva: HsvaColor): string => {\n  const { h, s, l, a } = hsvaToHsla(hsva);\n  return `hsla(${h}, ${s}%, ${l}%, ${a})`;\n};\n\nexport const hsvaToRgba = ({ h, s, v, a }: HsvaColor): RgbaColor => {\n  h = (h / 360) * 6;\n  s = s / 100;\n  v = v / 100;\n\n  const hh = Math.floor(h),\n    b = v * (1 - s),\n    c = v * (1 - (h - hh) * s),\n    d = v * (1 - (1 - h + hh) * s),\n    module = hh % 6;\n\n  return {\n    r: round([v, c, b, b, d, v][module] * 255),\n    g: round([d, v, v, c, b, b][module] * 255),\n    b: round([b, b, d, v, v, c][module] * 255),\n    a: round(a, 2),\n  };\n};\n\nexport const hsvaToRgbString = (hsva: HsvaColor): string => {\n  const { r, g, b } = hsvaToRgba(hsva);\n  return `rgb(${r}, ${g}, ${b})`;\n};\n\nexport const hsvaToRgbaString = (hsva: HsvaColor): string => {\n  const { r, g, b, a } = hsvaToRgba(hsva);\n  return `rgba(${r}, ${g}, ${b}, ${a})`;\n};\n\nexport const hsvaStringToHsva = (hsvString: string): HsvaColor => {\n  const matcher = /hsva?\\(?\\s*(-?\\d*\\.?\\d+)(deg|rad|grad|turn)?[,\\s]+(-?\\d*\\.?\\d+)%?[,\\s]+(-?\\d*\\.?\\d+)%?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i;\n  const match = matcher.exec(hsvString);\n\n  if (!match) return { h: 0, s: 0, v: 0, a: 1 };\n\n  return roundHsva({\n    h: parseHue(match[1], match[2]),\n    s: Number(match[3]),\n    v: Number(match[4]),\n    a: match[5] === undefined ? 1 : Number(match[5]) / (match[6] ? 100 : 1),\n  });\n};\n\nexport const hsvStringToHsva = hsvaStringToHsva;\n\nexport const rgbaStringToHsva = (rgbaString: string): HsvaColor => {\n  const matcher = /rgba?\\(?\\s*(-?\\d*\\.?\\d+)(%)?[,\\s]+(-?\\d*\\.?\\d+)(%)?[,\\s]+(-?\\d*\\.?\\d+)(%)?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i;\n  const match = matcher.exec(rgbaString);\n\n  if (!match) return { h: 0, s: 0, v: 0, a: 1 };\n\n  return rgbaToHsva({\n    r: Number(match[1]) / (match[2] ? 100 / 255 : 1),\n    g: Number(match[3]) / (match[4] ? 100 / 255 : 1),\n    b: Number(match[5]) / (match[6] ? 100 / 255 : 1),\n    a: match[7] === undefined ? 1 : Number(match[7]) / (match[8] ? 100 : 1),\n  });\n};\n\nexport const rgbStringToHsva = rgbaStringToHsva;\n\nconst format = (number: number) => {\n  const hex = number.toString(16);\n  return hex.length < 2 ? \"0\" + hex : hex;\n};\n\nexport const rgbaToHex = ({ r, g, b, a }: RgbaColor): string => {\n  const alphaHex = a < 1 ? format(round(a * 255)) : \"\";\n  return \"#\" + format(r) + format(g) + format(b) + alphaHex;\n};\n\nexport const rgbaToHsva = ({ r, g, b, a }: RgbaColor): HsvaColor => {\n  const max = Math.max(r, g, b);\n  const delta = max - Math.min(r, g, b);\n\n  // prettier-ignore\n  const hh = delta\n    ? max === r\n      ? (g - b) / delta\n      : max === g\n        ? 2 + (b - r) / delta\n        : 4 + (r - g) / delta\n    : 0;\n\n  return {\n    h: round(60 * (hh < 0 ? hh + 6 : hh)),\n    s: round(max ? (delta / max) * 100 : 0),\n    v: round((max / 255) * 100),\n    a,\n  };\n};\n\nexport const roundHsva = (hsva: HsvaColor): HsvaColor => ({\n  h: round(hsva.h),\n  s: round(hsva.s),\n  v: round(hsva.v),\n  a: round(hsva.a, 2),\n});\n\nexport const rgbaToRgb = ({ r, g, b }: RgbaColor): RgbColor => ({ r, g, b });\n\nexport const hslaToHsl = ({ h, s, l }: HslaColor): HslColor => ({ h, s, l });\n\nexport const hsvaToHsv = (hsva: HsvaColor): HsvColor => {\n  const { h, s, v } = roundHsva(hsva);\n  return { h, s, v };\n};\n", "import React from \"react\";\n\nimport { Interactive, Interaction } from \"./Interactive\";\nimport { Pointer } from \"./Pointer\";\n\nimport { hsvaToHslString } from \"../../utils/convert\";\nimport { formatClassName } from \"../../utils/format\";\nimport { clamp } from \"../../utils/clamp\";\nimport { round } from \"../../utils/round\";\n\ninterface Props {\n  className?: string;\n  hue: number;\n  onChange: (newHue: { h: number }) => void;\n}\n\nconst HueBase = ({ className, hue, onChange }: Props) => {\n  const handleMove = (interaction: Interaction) => {\n    onChange({ h: 360 * interaction.left });\n  };\n\n  const handleKey = (offset: Interaction) => {\n    // Hue measured in degrees of the color circle ranging from 0 to 360\n    onChange({\n      h: clamp(hue + offset.left * 360, 0, 360),\n    });\n  };\n\n  const nodeClassName = formatClassName([\"react-colorful__hue\", className]);\n\n  return (\n    <div className={nodeClassName}>\n      <Interactive\n        onMove={handleMove}\n        onKey={handleKey}\n        aria-label=\"Hue\"\n        aria-valuenow={round(hue)}\n        aria-valuemax=\"360\"\n        aria-valuemin=\"0\"\n      >\n        <Pointer\n          className=\"react-colorful__hue-pointer\"\n          left={hue / 360}\n          color={hsvaToHslString({ h: hue, s: 100, v: 100, a: 1 })}\n        />\n      </Interactive>\n    </div>\n  );\n};\n\nexport const Hue = React.memo(HueBase);\n", "import React from \"react\";\nimport { Interactive, Interaction } from \"./Interactive\";\nimport { Pointer } from \"./Pointer\";\nimport { HsvaColor } from \"../../types\";\nimport { hsvaToHslString } from \"../../utils/convert\";\nimport { clamp } from \"../../utils/clamp\";\nimport { round } from \"../../utils/round\";\n\ninterface Props {\n  hsva: HsvaColor;\n  onChange: (newColor: { s: number; v: number }) => void;\n}\n\nconst SaturationBase = ({ hsva, onChange }: Props) => {\n  const handleMove = (interaction: Interaction) => {\n    onChange({\n      s: interaction.left * 100,\n      v: 100 - interaction.top * 100,\n    });\n  };\n\n  const handleKey = (offset: Interaction) => {\n    // Saturation and brightness always fit into [0, 100] range\n    onChange({\n      s: clamp(hsva.s + offset.left * 100, 0, 100),\n      v: clamp(hsva.v - offset.top * 100, 0, 100),\n    });\n  };\n\n  const containerStyle = {\n    backgroundColor: hsvaToHslString({ h: hsva.h, s: 100, v: 100, a: 1 }),\n  };\n\n  return (\n    <div className=\"react-colorful__saturation\" style={containerStyle}>\n      <Interactive\n        onMove={handleMove}\n        onKey={handleKey}\n        aria-label=\"Color\"\n        aria-valuetext={`Saturation ${round(hsva.s)}%, Brightness ${round(hsva.v)}%`}\n      >\n        <Pointer\n          className=\"react-colorful__saturation-pointer\"\n          top={1 - hsva.v / 100}\n          left={hsva.s / 100}\n          color={hsvaToHslString(hsva)}\n        />\n      </Interactive>\n    </div>\n  );\n};\n\nexport const Saturation = React.memo(SaturationBase);\n", "import { hexToRgba } from \"./convert\";\nimport { ObjectColor } from \"../types\";\n\nexport const equalColorObjects = (first: ObjectColor, second: ObjectColor): boolean => {\n  if (first === second) return true;\n\n  for (const prop in first) {\n    // The following allows for a type-safe calling of this function (first & second have to be HSL, HSV, or RGB)\n    // with type-unsafe iterating over object keys. TS does not allow this without an index (`[key: string]: number`)\n    // on an object to define how iteration is normally done. To ensure extra keys are not allowed on our types,\n    // we must cast our object to unknown (as RGB demands `r` be a key, while `Record<string, x>` does not care if\n    // there is or not), and then as a type TS can iterate over.\n    if (\n      ((first as unknown) as Record<string, number>)[prop] !==\n      ((second as unknown) as Record<string, number>)[prop]\n    )\n      return false;\n  }\n\n  return true;\n};\n\nexport const equalColorString = (first: string, second: string): boolean => {\n  return first.replace(/\\s/g, \"\") === second.replace(/\\s/g, \"\");\n};\n\nexport const equalHex = (first: string, second: string): boolean => {\n  if (first.toLowerCase() === second.toLowerCase()) return true;\n\n  // To compare colors like `#FFF` and `ffffff` we convert them into RGB objects\n  return equalColorObjects(hexToRgba(first), hexToRgba(second));\n};\n", "import { useState, useEffect, useCallback, useRef } from \"react\";\nimport { ColorModel, AnyColor, HsvaColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { useEventCallback } from \"./useEventCallback\";\n\nexport function useColorManipulation<T extends AnyColor>(\n  colorModel: ColorModel<T>,\n  color: T,\n  onChange?: (color: T) => void\n): [HsvaColor, (color: Partial<HsvaColor>) => void] {\n  // Save onChange callback in the ref for avoiding \"useCallback hell\"\n  const onChangeCallback = useEventCallback<T>(onChange);\n\n  // No matter which color model is used (HEX, RGB(A) or HSL(A)),\n  // all internal calculations are based on HSVA model\n  const [hsva, updateHsva] = useState<HsvaColor>(() => colorModel.toHsva(color));\n\n  // By using this ref we're able to prevent extra updates\n  // and the effects recursion during the color conversion\n  const cache = useRef({ color, hsva });\n\n  // Update local HSVA-value if `color` property value is changed,\n  // but only if that's not the same color that we just sent to the parent\n  useEffect(() => {\n    if (!colorModel.equal(color, cache.current.color)) {\n      const newHsva = colorModel.toHsva(color);\n      cache.current = { hsva: newHsva, color };\n      updateHsva(newHsva);\n    }\n  }, [color, colorModel]);\n\n  // Trigger `onChange` callback only if an updated color is different from cached one;\n  // save the new color to the ref to prevent unnecessary updates\n  useEffect(() => {\n    let newColor;\n    if (\n      !equalColorObjects(hsva, cache.current.hsva) &&\n      !colorModel.equal((newColor = colorModel.fromHsva(hsva)), cache.current.color)\n    ) {\n      cache.current = { hsva, color: newColor };\n      onChangeCallback(newColor);\n    }\n  }, [hsva, colorModel, onChangeCallback]);\n\n  // Merge the current HSVA color object with updated params.\n  // For example, when a child component sends `h` or `s` only\n  const handleChange = useCallback((params: Partial<HsvaColor>) => {\n    updateHsva((current) => Object.assign({}, current, params));\n  }, []);\n\n  return [hsva, handleChange];\n}\n", "import { useLayoutEffect, useEffect } from \"react\";\n\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nexport const useIsomorphicLayoutEffect =\n  typeof window !== \"undefined\" ? useLayoutEffect : useEffect;\n", "declare const __webpack_nonce__: string | undefined;\nlet nonce: string | undefined;\n\n/**\n * Returns a nonce hash included by Webpack or the one defined manually by developer.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/nonce\n * https://webpack.js.org/guides/csp/\n */\nexport const getNonce = (): string | undefined => {\n  if (nonce) return nonce;\n  if (typeof __webpack_nonce__ !== \"undefined\") return __webpack_nonce__;\n  return undefined;\n};\n\n/**\n * Signs the style tag with a base64-encoded string (nonce) to conforms to Content Security Policies.\n * This function has to be invoked before any picker is rendered if you aren't using Webpack for CSP.\n */\nexport const setNonce = (hash: string): void => {\n  nonce = hash;\n};\n", "import { RefObject } from \"react\";\n\nimport { useIsomorphicLayoutEffect } from \"./useIsomorphicLayoutEffect\";\nimport { getNonce } from \"../utils/nonce\";\n\n// Bund<PERSON> is configured to load this as a processed minified CSS-string\nimport styles from \"../css/styles.css\";\n\nconst styleElementMap: Map<Document, HTMLStyleElement> = new Map();\n\n/**\n * Injects CSS code into the document's <head>\n */\nexport const useStyleSheet = (nodeRef: RefObject<HTMLDivElement>): void => {\n  useIsomorphicLayoutEffect(() => {\n    const parentDocument = nodeRef.current ? nodeRef.current.ownerDocument : document;\n\n    if (typeof parentDocument !== \"undefined\" && !styleElementMap.has(parentDocument)) {\n      const styleElement = parentDocument.createElement(\"style\");\n      styleElement.innerHTML = styles;\n      styleElementMap.set(parentDocument, styleElement);\n\n      // Conform to CSP rules by setting `nonce` attribute to the inline styles\n      const nonce = getNonce();\n      if (nonce) styleElement.setAttribute(\"nonce\", nonce);\n\n      parentDocument.head.appendChild(styleElement);\n    }\n  }, []);\n};\n", "import React, { useRef } from \"react\";\n\nimport { Hu<PERSON> } from \"./Hue\";\nimport { Saturation } from \"./Saturation\";\n\nimport { ColorModel, ColorPickerBaseProps, AnyColor } from \"../../types\";\nimport { useColorManipulation } from \"../../hooks/useColorManipulation\";\nimport { useStyleSheet } from \"../../hooks/useStyleSheet\";\nimport { formatClassName } from \"../../utils/format\";\n\ninterface Props<T extends AnyColor> extends Partial<ColorPickerBaseProps<T>> {\n  colorModel: ColorModel<T>;\n}\n\nexport const ColorPicker = <T extends AnyColor>({\n  className,\n  colorModel,\n  color = colorModel.defaultColor,\n  onChange,\n  ...rest\n}: Props<T>): JSX.Element => {\n  const nodeRef = useRef<HTMLDivElement>(null);\n  useStyleSheet(nodeRef);\n\n  const [hsva, updateHsva] = useColorManipulation<T>(colorModel, color, onChange);\n\n  const nodeClassName = formatClassName([\"react-colorful\", className]);\n\n  return (\n    <div {...rest} ref={nodeRef} className={nodeClassName}>\n      <Saturation hsva={hsva} onChange={updateHsva} />\n      <Hue hue={hsva.h} onChange={updateHsva} className=\"react-colorful__last-control\" />\n    </div>\n  );\n};\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalHex } from \"../utils/compare\";\nimport { hexToHsva, hsvaToHex } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"000\",\n  toHsva: hexToHsva,\n  fromHsva: ({ h, s, v }) => hsvaToHex({ h, s, v, a: 1 }),\n  equal: equalHex,\n};\n\nexport const HexColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { Interactive, Interaction } from \"./Interactive\";\nimport { Pointer } from \"./Pointer\";\n\nimport { hsvaToHslaString } from \"../../utils/convert\";\nimport { formatClassName } from \"../../utils/format\";\nimport { clamp } from \"../../utils/clamp\";\nimport { round } from \"../../utils/round\";\nimport { HsvaColor } from \"../../types\";\n\ninterface Props {\n  className?: string;\n  hsva: HsvaColor;\n  onChange: (newAlpha: { a: number }) => void;\n}\n\nexport const Alpha = ({ className, hsva, onChange }: Props): JSX.Element => {\n  const handleMove = (interaction: Interaction) => {\n    onChange({ a: interaction.left });\n  };\n\n  const handleKey = (offset: Interaction) => {\n    // Alpha always fit into [0, 1] range\n    onChange({ a: clamp(hsva.a + offset.left) });\n  };\n\n  // We use `Object.assign` instead of the spread operator\n  // to prevent adding the polyfill (about 150 bytes gzipped)\n  const colorFrom = hsvaToHslaString(Object.assign({}, hsva, { a: 0 }));\n  const colorTo = hsvaToHslaString(Object.assign({}, hsva, { a: 1 }));\n\n  const gradientStyle = {\n    backgroundImage: `linear-gradient(90deg, ${colorFrom}, ${colorTo})`,\n  };\n\n  const nodeClassName = formatClassName([\"react-colorful__alpha\", className]);\n  const ariaValue = round(hsva.a * 100);\n\n  return (\n    <div className={nodeClassName}>\n      <div className=\"react-colorful__alpha-gradient\" style={gradientStyle} />\n      <Interactive\n        onMove={handleMove}\n        onKey={handleKey}\n        aria-label=\"Alpha\"\n        aria-valuetext={`${ariaValue}%`}\n        aria-valuenow={ariaValue}\n        aria-valuemin=\"0\"\n        aria-valuemax=\"100\"\n      >\n        <Pointer\n          className=\"react-colorful__alpha-pointer\"\n          left={hsva.a}\n          color={hsvaToHslaString(hsva)}\n        />\n      </Interactive>\n    </div>\n  );\n};\n", "import React, { useRef } from \"react\";\n\nimport { Hu<PERSON> } from \"./Hue\";\nimport { Saturation } from \"./Saturation\";\nimport { Alpha } from \"./Alpha\";\n\nimport { ColorModel, ColorPickerBaseProps, AnyColor } from \"../../types\";\nimport { useColorManipulation } from \"../../hooks/useColorManipulation\";\nimport { useStyleSheet } from \"../../hooks/useStyleSheet\";\nimport { formatClassName } from \"../../utils/format\";\n\ninterface Props<T extends AnyColor> extends Partial<ColorPickerBaseProps<T>> {\n  colorModel: ColorModel<T>;\n}\n\nexport const AlphaColorPicker = <T extends AnyColor>({\n  className,\n  colorModel,\n  color = colorModel.defaultColor,\n  onChange,\n  ...rest\n}: Props<T>): JSX.Element => {\n  const nodeRef = useRef<HTMLDivElement>(null);\n  useStyleSheet(nodeRef);\n\n  const [hsva, updateHsva] = useColorManipulation<T>(colorModel, color, onChange);\n\n  const nodeClassName = formatClassName([\"react-colorful\", className]);\n\n  return (\n    <div {...rest} ref={nodeRef} className={nodeClassName}>\n      <Saturation hsva={hsva} onChange={updateHsva} />\n      <Hue hue={hsva.h} onChange={updateHsva} />\n      <Alpha hsva={hsva} onChange={updateHsva} className=\"react-colorful__last-control\" />\n    </div>\n  );\n};\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalHex } from \"../utils/compare\";\nimport { hexToHsva, hsvaToHex } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"0001\",\n  toHsva: hexToHsva,\n  fromHsva: hsvaToHex,\n  equal: equalHex,\n};\n\nexport const HexAlphaColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <AlphaColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, HslaColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { hslaToHsva, hsvaToHsla } from \"../utils/convert\";\n\nconst colorModel: ColorModel<HslaColor> = {\n  defaultColor: { h: 0, s: 0, l: 0, a: 1 },\n  toHsva: hslaToHsva,\n  fromHsva: hsvaToHsla,\n  equal: equalColorObjects,\n};\n\nexport const HslaColorPicker = (props: Partial<ColorPickerBaseProps<HslaColor>>): JSX.Element => (\n  <AlphaColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { hslaStringToHsva, hsvaToHslaString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"hsla(0, 0%, 0%, 1)\",\n  toHsva: hslaStringToHsva,\n  fromHsva: hsvaToHslaString,\n  equal: equalColorString,\n};\n\nexport const HslaStringColorPicker = (\n  props: Partial<ColorPickerBaseProps<string>>\n): JSX.Element => <AlphaColorPicker {...props} colorModel={colorModel} />;\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, HslColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { hslaToHsva, hsvaToHsla, hslaToHsl } from \"../utils/convert\";\n\nconst colorModel: ColorModel<HslColor> = {\n  defaultColor: { h: 0, s: 0, l: 0 },\n  toHsva: ({ h, s, l }) => hslaToHsva({ h, s, l, a: 1 }),\n  fromHsva: (hsva) => hslaToHsl(hsvaToHsla(hsva)),\n  equal: equalColorObjects,\n};\n\nexport const HslColorPicker = (props: Partial<ColorPickerBaseProps<HslColor>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { hslStringToHsva, hsvaToHslString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"hsl(0, 0%, 0%)\",\n  toHsva: hslStringToHsva,\n  fromHsva: hsvaToHslString,\n  equal: equalColorString,\n};\n\nexport const HslStringColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, HsvaColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { roundHsva } from \"../utils/convert\";\n\nconst colorModel: ColorModel<HsvaColor> = {\n  defaultColor: { h: 0, s: 0, v: 0, a: 1 },\n  toHsva: (hsva) => hsva,\n  fromHsva: roundHsva,\n  equal: equalColorObjects,\n};\n\nexport const HsvaColorPicker = (props: Partial<ColorPickerBaseProps<HsvaColor>>): JSX.Element => (\n  <AlphaColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { hsvaStringToHsva, hsvaToHsvaString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"hsva(0, 0%, 0%, 1)\",\n  toHsva: hsvaStringToHsva,\n  fromHsva: hsvaToHsvaString,\n  equal: equalColorString,\n};\n\nexport const HsvaStringColorPicker = (\n  props: Partial<ColorPickerBaseProps<string>>\n): JSX.Element => <AlphaColorPicker {...props} colorModel={colorModel} />;\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, HsvColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { hsvaToHsv } from \"../utils/convert\";\n\nconst colorModel: ColorModel<HsvColor> = {\n  defaultColor: { h: 0, s: 0, v: 0 },\n  toHsva: ({ h, s, v }) => ({ h, s, v, a: 1 }),\n  fromHsva: hsvaToHsv,\n  equal: equalColorObjects,\n};\n\nexport const HsvColorPicker = (props: Partial<ColorPickerBaseProps<HsvColor>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { hsvStringToHsva, hsvaToHsvString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"hsv(0, 0%, 0%)\",\n  toHsva: hsvStringToHsva,\n  fromHsva: hsvaToHsvString,\n  equal: equalColorString,\n};\n\nexport const HsvStringColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, RgbaColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { rgbaToHsva, hsvaToRgba } from \"../utils/convert\";\n\nconst colorModel: ColorModel<RgbaColor> = {\n  defaultColor: { r: 0, g: 0, b: 0, a: 1 },\n  toHsva: rgbaToHsva,\n  fromHsva: hsvaToRgba,\n  equal: equalColorObjects,\n};\n\nexport const RgbaColorPicker = (props: Partial<ColorPickerBaseProps<RgbaColor>>): JSX.Element => (\n  <AlphaColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { rgbaStringToHsva, hsvaToRgbaString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"rgba(0, 0, 0, 1)\",\n  toHsva: rgbaStringToHsva,\n  fromHsva: hsvaToRgbaString,\n  equal: equalColorString,\n};\n\nexport const RgbaStringColorPicker = (\n  props: Partial<ColorPickerBaseProps<string>>\n): JSX.Element => <AlphaColorPicker {...props} colorModel={colorModel} />;\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, RgbColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { rgbaToHsva, hsvaToRgba, rgbaToRgb } from \"../utils/convert\";\n\nconst colorModel: ColorModel<RgbColor> = {\n  defaultColor: { r: 0, g: 0, b: 0 },\n  toHsva: ({ r, g, b }) => rgbaToHsva({ r, g, b, a: 1 }),\n  fromHsva: (hsva) => rgbaToRgb(hsvaToRgba(hsva)),\n  equal: equalColorObjects,\n};\n\nexport const RgbColorPicker = (props: Partial<ColorPickerBaseProps<RgbColor>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { rgbStringToHsva, hsvaToRgbString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"rgb(0, 0, 0)\",\n  toHsva: rgbStringToHsva,\n  fromHsva: hsvaToRgbString,\n  equal: equalColorString,\n};\n\nexport const RgbStringColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "const matcher = /^#?([0-9A-F]{3,8})$/i;\n\nexport const validHex = (value: string, alpha?: boolean): boolean => {\n  const match = matcher.exec(value);\n  const length = match ? match[1].length : 0;\n\n  return (\n    length === 3 || // '#rgb' format\n    length === 6 || // '#rrggbb' format\n    (!!alpha && length === 4) || // '#rgba' format\n    (!!alpha && length === 8) // '#rrggbbaa' format\n  );\n};\n", "import React, { useState, useEffect, useCallback } from \"react\";\n\nimport { useEventCallback } from \"../../hooks/useEventCallback\";\nimport { ColorInputBaseProps } from \"../../types\";\n\ninterface Props extends ColorInputBaseProps {\n  /** Blocks typing invalid characters and limits string length */\n  escape: (value: string) => string;\n  /** Checks that value is valid color string */\n  validate: (value: string) => boolean;\n  /** Processes value before displaying it in the input */\n  format?: (value: string) => string;\n  /** Processes value before sending it in `onChange` */\n  process?: (value: string) => string;\n}\n\nexport const ColorInput = (props: Props): JSX.Element => {\n  const { color = \"\", onChange, onBlur, escape, validate, format, process, ...rest } = props;\n  const [value, setValue] = useState(() => escape(color));\n  const onChangeCallback = useEventCallback<string>(onChange);\n  const onBlurCallback = useEventCallback<React.FocusEvent<HTMLInputElement>>(onBlur);\n\n  // Trigger `onChange` handler only if the input value is a valid color\n  const handleChange = useCallback(\n    (e: React.ChangeEvent<HTMLInputElement>) => {\n      const inputValue = escape(e.target.value);\n      setValue(inputValue);\n      if (validate(inputValue)) onChangeCallback(process ? process(inputValue) : inputValue);\n    },\n    [escape, process, validate, onChangeCallback]\n  );\n\n  // Take the color from props if the last typed color (in local state) is not valid\n  const handleBlur = useCallback(\n    (e: React.FocusEvent<HTMLInputElement>) => {\n      if (!validate(e.target.value)) setValue(escape(color));\n      onBlurCallback(e);\n    },\n    [color, escape, validate, onBlurCallback]\n  );\n\n  // Update the local state when `color` property value is changed\n  useEffect(() => {\n    setValue(escape(color));\n  }, [color, escape]);\n\n  return (\n    <input\n      {...rest}\n      value={format ? format(value) : value}\n      spellCheck=\"false\" // the element should not be checked for spelling errors\n      onChange={handleChange}\n      onBlur={handleBlur}\n    />\n  );\n};\n", "import React, { useCallback } from \"react\";\nimport { ColorInputBaseProps } from \"../types\";\n\nimport { validHex } from \"../utils/validate\";\nimport { ColorInput } from \"./common/ColorInput\";\n\ninterface HexColorInputProps extends ColorInputBaseProps {\n  /** Enables `#` prefix displaying */\n  prefixed?: boolean;\n  /** Allows `#rgba` and `#rrggbbaa` color formats */\n  alpha?: boolean;\n}\n\n/** Adds \"#\" symbol to the beginning of the string */\nconst prefix = (value: string) => \"#\" + value;\n\nexport const HexColorInput = (props: HexColorInputProps): JSX.Element => {\n  const { prefixed, alpha, ...rest } = props;\n\n  /** Escapes all non-hexadecimal characters including \"#\" */\n  const escape = useCallback(\n    (value: string) => value.replace(/([^0-9A-F]+)/gi, \"\").substring(0, alpha ? 8 : 6),\n    [alpha]\n  );\n\n  /** Validates hexadecimal strings */\n  const validate = useCallback((value: string) => validHex(value, alpha), [alpha]);\n\n  return (\n    <ColorInput\n      {...rest}\n      escape={escape}\n      format={prefixed ? prefix : undefined}\n      process={prefix}\n      validate={validate}\n    />\n  );\n};\n"], "names": ["useEventCallback", "handler", "callback<PERSON><PERSON>", "useRef", "fn", "value", "current", "clamp", "number", "min", "max", "is<PERSON><PERSON>ch", "event", "getParentWindow", "node", "ownerDocument", "defaultView", "self", "getRelativePosition", "touchId", "rect", "getBoundingClientRect", "pointer", "touches", "i", "length", "identifier", "getTouchPoint", "left", "pageX", "pageXOffset", "width", "top", "pageY", "pageYOffset", "height", "preventDefaultMove", "preventDefault", "Interactive", "React", "memo", "onMove", "onKey", "rest", "container", "onMoveCallback", "onKeyCallback", "has<PERSON><PERSON><PERSON>", "handleMoveStart", "handleKeyDown", "toggleDocumentEvents", "useMemo", "handleMove", "buttons", "handleMoveEnd", "state", "touch", "parentWindow", "toggleEvent", "addEventListener", "removeEventListener", "nativeEvent", "el", "isInvalid", "changedTouches", "focus", "keyCode", "which", "useEffect", "onTouchStart", "onMouseDown", "className", "ref", "onKeyDown", "tabIndex", "role", "formatClassName", "names", "filter", "Boolean", "join", "Pointer", "color", "nodeClassName", "style", "backgroundColor", "round", "digits", "base", "Math", "pow", "angleUnits", "grad", "turn", "rad", "PI", "hexToHsva", "hex", "rgbaToHsva", "hexToRgba", "substring", "r", "parseInt", "g", "b", "a", "parseHue", "unit", "Number", "hslaStringToHsva", "hslString", "match", "exec", "hslaToHsva", "h", "s", "l", "undefined", "v", "hslStringToHsva", "hsvaToHex", "hsva", "rgbaToHex", "hsvaToRgba", "hsvaToHsla", "hh", "hsvaToHslString", "hsvaToHslaString", "floor", "c", "d", "module", "hsvaStringToHsva", "hsvString", "roundHsva", "hsvStringToHsva", "rgbaStringToHsva", "rgbaString", "rgbStringToHsva", "format", "toString", "alphaHex", "delta", "<PERSON><PERSON>", "hue", "onChange", "interaction", "offset", "aria-label", "aria-valuenow", "aria-valuemax", "aria-valuemin", "Saturation", "containerStyle", "aria-valuetext", "equalColorObjects", "first", "second", "prop", "equalColorString", "replace", "equalHex", "toLowerCase", "useColorManipulation", "colorModel", "onChangeCallback", "updateHsva", "useState", "toHsva", "cache", "equal", "newHsva", "newColor", "fromHsva", "handleChange", "useCallback", "params", "Object", "assign", "useIsomorphicLayoutEffect", "window", "useLayoutEffect", "nonce", "getNonce", "__webpack_nonce__", "setNonce", "hash", "styleElementMap", "Map", "useStyleSheet", "nodeRef", "parentDocument", "document", "has", "styleElement", "createElement", "innerHTML", "set", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "ColorPicker", "defaultColor", "HexColorPicker", "props", "Alpha", "gradientStyle", "backgroundImage", "ariaValue", "AlphaColorPicker", "HexAlphaColorPicker", "HslaColorPicker", "HslaStringColorPicker", "hslaToHsl", "HslColorPicker", "HslStringColorPicker", "HsvaColorPicker", "HsvaStringColorPicker", "HsvColorPicker", "HsvStringColorPicker", "RgbaColorPicker", "RgbaStringColorPicker", "rgbaToRgb", "RgbColorPicker", "RgbStringColorPicker", "matcher", "ColorInput", "onBlur", "escape", "validate", "process", "setValue", "onBlurCallback", "e", "inputValue", "target", "handleBlur", "spell<PERSON>heck", "prefix", "HexColorInput", "prefixed", "alpha", "validHex"], "mappings": "8cAGgBA,EAAoBC,GAClC,MAAMC,EAAcC,EAAOF,GACrBG,EAAKD,EAAQE,IACjBH,EAAYI,SAAWJ,EAAYI,QAAQD,KAI7C,OAFAH,EAAYI,QAAUL,EAEfG,EAAGE,cCPCC,EAAQ,CAACC,EAAgBC,EAAM,EAAGC,EAAM,IAC5CF,EAASE,EAAMA,EAAMF,EAASC,EAAMA,EAAMD,ECO7CG,EAAWC,GAAwD,YAAaA,EAWhFC,EAAmBC,GACfA,GAAQA,EAAKC,cAAcC,aAAgBC,KAI/CC,EAAsB,CAC1BJ,EACAF,EACAO,KAEA,MAAMC,EAAON,EAAKO,wBAGZC,EAAUX,EAAQC,GArBJ,EAACW,EAAoBJ,KACzC,IAAK,IAAIK,EAAI,EAAGA,EAAID,EAAQE,OAAQD,IAClC,GAAID,EAAQC,GAAGE,aAAeP,EAAS,OAAOI,EAAQC,GAExD,OAAOD,EAAQ,IAiBkBI,CAAcf,EAAMW,QAASJ,GAAYP,EAE1E,MAAO,CACLgB,KAAMrB,GAAOe,EAAQO,OAAST,EAAKQ,KAAOf,EAAgBC,GAAMgB,cAAgBV,EAAKW,OACrFC,IAAKzB,GAAOe,EAAQW,OAASb,EAAKY,IAAMnB,EAAgBC,GAAMoB,cAAgBd,EAAKe,UAOjFC,EAAsBxB,KACzBD,EAAQC,IAAUA,EAAMyB,kBA8GdC,EAAcC,EAAMC,aA/FRC,OAAEA,EAAFC,MAAUA,KAAUC,0BAC3C,MAAMC,EAAYzC,EAAuB,MACnC0C,EAAiB7C,EAA8ByC,GAC/CK,EAAgB9C,EAA8B0C,GAC9CvB,EAAUhB,EAAsB,MAChC4C,EAAW5C,GAAO,IAEjB6C,EAAiBC,EAAeC,GAAwBC,EAAQ,KACrE,MAoBMC,EAAcxC,IAElBwB,EAAmBxB,IAOJD,EAAQC,GAASA,EAAMW,QAAQE,OAAS,EAAIb,EAAMyC,QAAU,IAE7DT,EAAUtC,QACtBuC,EAAe3B,EAAoB0B,EAAUtC,QAASM,EAAOO,EAAQb,UAErE4C,GAAqB,IAInBI,EAAgB,IAAMJ,GAAqB,GAkBjD,SAASA,EAAqBK,GAC5B,MAAMC,EAAQT,EAASzC,QAEjBmD,EAAe5C,EADV+B,EAAUtC,SAIfoD,EAAcH,EAAQE,EAAaE,iBAAmBF,EAAaG,oBACzEF,EAAYF,EAAQ,YAAc,YAAaJ,GAC/CM,EAAYF,EAAQ,WAAa,UAAWF,GAG9C,MAAO,CAnEiB,EAAGO,YAAAA,MACzB,MAAMC,EAAKlB,EAAUtC,QACrB,GAAKwD,IAGL1B,EAAmByB,IAvBP,EAACjD,EAAgCmC,IAC1CA,IAAapC,EAAQC,GAwBpBmD,CAAUF,EAAad,EAASzC,UAAawD,GAAjD,CAEA,GAAInD,EAAQkD,GAAc,CACxBd,EAASzC,SAAU,EACnB,MAAM0D,EAAiBH,EAAYG,gBAAkB,GACjDA,EAAevC,SAAQN,EAAQb,QAAU0D,EAAe,GAAGtC,YAGjEoC,EAAGG,QACHpB,EAAe3B,EAAoB4C,EAAID,EAAa1C,EAAQb,UAC5D4C,GAAqB,KAuBAtC,IACrB,MAAMsD,EAAUtD,EAAMuD,OAASvD,EAAMsD,QAGjCA,EAAU,IAAMA,EAAU,KAE9BtD,EAAMyB,iBAINS,EAAc,CACZlB,KAAkB,KAAZsC,EAAiB,IAAmB,KAAZA,GAAkB,IAAO,EACvDlC,IAAiB,KAAZkC,EAAiB,IAAmB,KAAZA,GAAkB,IAAO,MAelBhB,IACvC,CAACJ,EAAeD,IAKnB,OAFAuB,EAAU,IAAMlB,EAAsB,CAACA,IAGrCX,2BACMI,GACJ0B,aAAcrB,EACdsB,YAAatB,EACbuB,UAAU,8BACVC,IAAK5B,EACL6B,UAAWxB,EACXyB,SAAU,EACVC,KAAK,cCxJEC,EAAmBC,GAA6BA,EAAMC,OAAOC,SAASC,KAAK,KCU3EC,EAAU,EAAGV,UAAAA,EAAWW,MAAAA,EAAOtD,KAAAA,EAAMI,IAAAA,EAAM,OACtD,MAAMmD,EAAgBP,EAAgB,CAAC,0BAA2BL,IAOlE,OACEhC,uBAAKgC,UAAWY,EAAeC,MANnB,CACZpD,IAAc,IAANA,MACRJ,KAAgB,IAAPA,QAKPW,uBAAKgC,UAAU,+BAA+Ba,MAAO,CAAEC,gBAAiBH,OCpBjEI,EAAQ,CAAC9E,EAAgB+E,EAAS,EAAGC,EAAOC,KAAKC,IAAI,GAAIH,KAC7DE,KAAKH,MAAME,EAAOhF,GAAUgF,ECM/BG,EAAqC,CACzCC,KAAM,GACNC,KAAM,IACNC,IAAK,KAAiB,EAAVL,KAAKM,KAGNC,EAAaC,GAA2BC,EAAWC,EAAUF,IAE7DE,EAAaF,IACT,MAAXA,EAAI,KAAYA,EAAMA,EAAIG,UAAU,IAEpCH,EAAIxE,OAAS,EACR,CACL4E,EAAGC,SAASL,EAAI,GAAKA,EAAI,GAAI,IAC7BM,EAAGD,SAASL,EAAI,GAAKA,EAAI,GAAI,IAC7BO,EAAGF,SAASL,EAAI,GAAKA,EAAI,GAAI,IAC7BQ,EAAkB,IAAfR,EAAIxE,OAAe6D,EAAMgB,SAASL,EAAI,GAAKA,EAAI,GAAI,IAAM,IAAK,GAAK,GAInE,CACLI,EAAGC,SAASL,EAAIG,UAAU,EAAG,GAAI,IACjCG,EAAGD,SAASL,EAAIG,UAAU,EAAG,GAAI,IACjCI,EAAGF,SAASL,EAAIG,UAAU,EAAG,GAAI,IACjCK,EAAkB,IAAfR,EAAIxE,OAAe6D,EAAMgB,SAASL,EAAIG,UAAU,EAAG,GAAI,IAAM,IAAK,GAAK,IAIjEM,EAAW,CAACrG,EAAesG,EAAO,QACtCC,OAAOvG,IAAUsF,EAAWgB,IAAS,GAGjCE,EAAoBC,IAC/B,MACMC,EADU,6HACMC,KAAKF,GAE3B,OAAKC,EAEEE,EAAW,CAChBC,EAAGR,EAASK,EAAM,GAAIA,EAAM,IAC5BI,EAAGP,OAAOG,EAAM,IAChBK,EAAGR,OAAOG,EAAM,IAChBN,OAAgBY,IAAbN,EAAM,GAAmB,EAAIH,OAAOG,EAAM,KAAOA,EAAM,GAAK,IAAM,KANpD,CAAEG,EAAG,EAAGC,EAAG,EAAGG,EAAG,EAAGb,EAAG,IAU/Bc,EAAkBV,EAElBI,EAAa,EAAGC,EAAAA,EAAGC,EAAAA,EAAGC,EAAAA,EAAGX,EAAAA,MAG7B,CACLS,EAAGA,EACHC,GAJFA,IAAMC,EAAI,GAAKA,EAAI,IAAMA,GAAK,KAIrB,EAAM,EAAID,GAAMC,EAAID,GAAM,IAAM,EACvCG,EAAGF,EAAID,EACPV,EAAAA,IAISe,EAAaC,GAA4BC,EAAUC,EAAWF,IAE9DG,EAAa,EAAGV,EAAAA,EAAGC,EAAAA,EAAGG,EAAAA,EAAGb,EAAAA,MACpC,MAAMoB,GAAO,IAAMV,GAAKG,EAAK,IAE7B,MAAO,CACLJ,EAAG5B,EAAM4B,GACTC,EAAG7B,EAAMuC,EAAK,GAAKA,EAAK,IAAQV,EAAIG,EAAK,KAAOO,GAAM,IAAMA,EAAK,IAAMA,GAAO,IAAM,GACpFT,EAAG9B,EAAMuC,EAAK,GACdpB,EAAGnB,EAAMmB,EAAG,KAIHqB,EAAmBL,IAC9B,MAAMP,EAAEA,EAAFC,EAAKA,EAALC,EAAQA,GAAMQ,EAAWH,GAC/B,aAAcP,MAAMC,OAAOC,OAahBW,EAAoBN,IAC/B,MAAMP,EAAEA,EAAFC,EAAKA,EAALC,EAAQA,EAARX,GAAiBmB,EAAWH,GAClC,cAAeP,MAAMC,OAAOC,OAAOX,MAGxBkB,EAAa,EAAGT,EAAAA,EAAGC,EAAAA,EAAGG,EAAAA,EAAGb,EAAAA,MACpCS,EAAKA,EAAI,IAAO,EAChBC,GAAQ,IACRG,GAAQ,IAER,MAAMO,EAAKpC,KAAKuC,MAAMd,GACpBV,EAAIc,GAAK,EAAIH,GACbc,EAAIX,GAAK,GAAKJ,EAAIW,GAAMV,GACxBe,EAAIZ,GAAK,GAAK,EAAIJ,EAAIW,GAAMV,GAC5BgB,EAASN,EAAK,EAEhB,MAAO,CACLxB,EAAGf,EAAmC,IAA7B,CAACgC,EAAGW,EAAGzB,EAAGA,EAAG0B,EAAGZ,GAAGa,IAC5B5B,EAAGjB,EAAmC,IAA7B,CAAC4C,EAAGZ,EAAGA,EAAGW,EAAGzB,EAAGA,GAAG2B,IAC5B3B,EAAGlB,EAAmC,IAA7B,CAACkB,EAAGA,EAAG0B,EAAGZ,EAAGA,EAAGW,GAAGE,IAC5B1B,EAAGnB,EAAMmB,EAAG,KAcH2B,EAAoBC,IAC/B,MACMtB,EADU,6HACMC,KAAKqB,GAE3B,OAAKtB,EAEEuB,EAAU,CACfpB,EAAGR,EAASK,EAAM,GAAIA,EAAM,IAC5BI,EAAGP,OAAOG,EAAM,IAChBO,EAAGV,OAAOG,EAAM,IAChBN,OAAgBY,IAAbN,EAAM,GAAmB,EAAIH,OAAOG,EAAM,KAAOA,EAAM,GAAK,IAAM,KANpD,CAAEG,EAAG,EAAGC,EAAG,EAAGG,EAAG,EAAGb,EAAG,IAU/B8B,EAAkBH,EAElBI,EAAoBC,IAC/B,MACM1B,EADU,iHACMC,KAAKyB,GAE3B,OAAK1B,EAEEb,EAAW,CAChBG,EAAGO,OAAOG,EAAM,KAAOA,EAAM,GAAK,IAAM,IAAM,GAC9CR,EAAGK,OAAOG,EAAM,KAAOA,EAAM,GAAK,IAAM,IAAM,GAC9CP,EAAGI,OAAOG,EAAM,KAAOA,EAAM,GAAK,IAAM,IAAM,GAC9CN,OAAgBY,IAAbN,EAAM,GAAmB,EAAIH,OAAOG,EAAM,KAAOA,EAAM,GAAK,IAAM,KANpD,CAAEG,EAAG,EAAGC,EAAG,EAAGG,EAAG,EAAGb,EAAG,IAU/BiC,EAAkBF,EAEzBG,EAAUnI,IACd,MAAMyF,EAAMzF,EAAOoI,SAAS,IAC5B,OAAO3C,EAAIxE,OAAS,EAAI,IAAMwE,EAAMA,GAGzByB,EAAY,EAAGrB,EAAAA,EAAGE,EAAAA,EAAGC,EAAAA,EAAGC,EAAAA,MACnC,MAAMoC,EAAWpC,EAAI,EAAIkC,EAAOrD,EAAU,IAAJmB,IAAY,GAClD,MAAO,IAAMkC,EAAOtC,GAAKsC,EAAOpC,GAAKoC,EAAOnC,GAAKqC,GAGtC3C,EAAa,EAAGG,EAAAA,EAAGE,EAAAA,EAAGC,EAAAA,EAAGC,EAAAA,MACpC,MAAM/F,EAAM+E,KAAK/E,IAAI2F,EAAGE,EAAGC,GACrBsC,EAAQpI,EAAM+E,KAAKhF,IAAI4F,EAAGE,EAAGC,GAG7BqB,EAAKiB,EACPpI,IAAQ2F,GACLE,EAAIC,GAAKsC,EACVpI,IAAQ6F,EACN,GAAKC,EAAIH,GAAKyC,EACd,GAAKzC,EAAIE,GAAKuC,EAClB,EAEJ,MAAO,CACL5B,EAAG5B,EAAM,IAAMuC,EAAK,EAAIA,EAAK,EAAIA,IACjCV,EAAG7B,EAAM5E,EAAOoI,EAAQpI,EAAO,IAAM,GACrC4G,EAAGhC,EAAO5E,EAAM,IAAO,KACvB+F,EAAAA,IAIS6B,EAAab,KACxBP,EAAG5B,EAAMmC,EAAKP,GACdC,EAAG7B,EAAMmC,EAAKN,GACdG,EAAGhC,EAAMmC,EAAKH,GACdb,EAAGnB,EAAMmC,EAAKhB,EAAG,KCjJNsC,EAAMxG,EAAMC,KAlCT,EAAG+B,UAAAA,EAAWyE,IAAAA,EAAKC,SAAAA,MACjC,MAWM9D,EAAgBP,EAAgB,CAAC,sBAAuBL,IAE9D,OACEhC,uBAAKgC,UAAWY,GACd5C,gBAACD,GACCG,OAhBcyG,IAClBD,EAAS,CAAE/B,EAAG,IAAMgC,EAAYtH,QAgB5Bc,MAbayG,IAEjBF,EAAS,CACP/B,EAAG3G,EAAMyI,EAAoB,IAAdG,EAAOvH,KAAY,EAAG,QAWnCwH,aAAW,MACXC,gBAAe/D,EAAM0D,GACrBM,gBAAc,MACdC,gBAAc,KAEdhH,gBAAC0C,GACCV,UAAU,8BACV3C,KAAMoH,EAAM,IACZ9D,MAAO4C,EAAgB,CAAEZ,EAAG8B,EAAK7B,EAAG,IAAKG,EAAG,IAAKb,EAAG,UCSjD+C,EAAajH,EAAMC,KAvCT,EAAGiF,KAAAA,EAAMwB,SAAAA,MAC9B,MAeMQ,EAAiB,CACrBpE,gBAAiByC,EAAgB,CAAEZ,EAAGO,EAAKP,EAAGC,EAAG,IAAKG,EAAG,IAAKb,EAAG,KAGnE,OACElE,uBAAKgC,UAAU,6BAA6Ba,MAAOqE,GACjDlH,gBAACD,GACCG,OAtBcyG,IAClBD,EAAS,CACP9B,EAAsB,IAAnB+B,EAAYtH,KACf0F,EAAG,IAAwB,IAAlB4B,EAAYlH,OAoBnBU,MAhBayG,IAEjBF,EAAS,CACP9B,EAAG5G,EAAMkH,EAAKN,EAAkB,IAAdgC,EAAOvH,KAAY,EAAG,KACxC0F,EAAG/G,EAAMkH,EAAKH,EAAiB,IAAb6B,EAAOnH,IAAW,EAAG,QAarCoH,aAAW,QACXM,+BAA8BpE,EAAMmC,EAAKN,mBAAmB7B,EAAMmC,EAAKH,OAEvE/E,gBAAC0C,GACCV,UAAU,qCACVvC,IAAK,EAAIyF,EAAKH,EAAI,IAClB1F,KAAM6F,EAAKN,EAAI,IACfjC,MAAO4C,EAAgBL,SC1CpBkC,EAAoB,CAACC,EAAoBC,KACpD,GAAID,IAAUC,EAAQ,SAEtB,IAAK,MAAMC,KAAQF,EAMjB,GACIA,EAA6CE,KAC7CD,EAA8CC,GAEhD,SAGJ,UAGWC,EAAmB,CAACH,EAAeC,IACvCD,EAAMI,QAAQ,MAAO,MAAQH,EAAOG,QAAQ,MAAO,IAG/CC,EAAW,CAACL,EAAeC,IAClCD,EAAMM,gBAAkBL,EAAOK,eAG5BP,EAAkBxD,EAAUyD,GAAQzD,EAAU0D,aCzBvCM,EACdC,EACAlF,EACA+D,GAGA,MAAMoB,EAAmBrK,EAAoBiJ,IAItCxB,EAAM6C,GAAcC,EAAoB,IAAMH,EAAWI,OAAOtF,IAIjEuF,EAAQtK,EAAO,CAAE+E,MAAAA,EAAOuC,KAAAA,IAI9BrD,EAAU,KACR,IAAKgG,EAAWM,MAAMxF,EAAOuF,EAAMnK,QAAQ4E,OAAQ,CACjD,MAAMyF,EAAUP,EAAWI,OAAOtF,GAClCuF,EAAMnK,QAAU,CAAEmH,KAAMkD,EAASzF,MAAAA,GACjCoF,EAAWK,KAEZ,CAACzF,EAAOkF,IAIXhG,EAAU,KACR,IAAIwG,EAEDjB,EAAkBlC,EAAMgD,EAAMnK,QAAQmH,OACtC2C,EAAWM,MAAOE,EAAWR,EAAWS,SAASpD,GAAQgD,EAAMnK,QAAQ4E,SAExEuF,EAAMnK,QAAU,CAAEmH,KAAAA,EAAMvC,MAAO0F,GAC/BP,EAAiBO,KAElB,CAACnD,EAAM2C,EAAYC,IAItB,MAAMS,EAAeC,EAAaC,IAChCV,EAAYhK,GAAY2K,OAAOC,OAAO,GAAI5K,EAAS0K,KAClD,IAEH,MAAO,CAACvD,EAAMqD,SC7CHK,EACO,oBAAXC,OAAyBC,EAAkBjH,ECLpD,IAAIkH,QAOSC,EAAW,IAClBD,IAC6B,oBAAtBE,kBAA0CA,uBAArD,GAQWC,EAAYC,IACvBJ,EAAQI,GCXJC,EAAmD,IAAIC,IAKhDC,EAAiBC,IAC5BX,EAA0B,KACxB,MAAMY,EAAiBD,EAAQxL,QAAUwL,EAAQxL,QAAQS,cAAgBiL,SAEzE,QAA8B,IAAnBD,IAAmCJ,EAAgBM,IAAIF,GAAiB,CACjF,MAAMG,EAAeH,EAAeI,cAAc,SAClDD,EAAaE,4tDACbT,EAAgBU,IAAIN,EAAgBG,GAGpC,MAAMZ,EAAQC,IACVD,GAAOY,EAAaI,aAAa,QAAShB,GAE9CS,EAAeQ,KAAKC,YAAYN,KAEjC,KCdQO,UAAmClI,UAC9CA,EAD8C6F,WAE9CA,EAF8ClF,MAG9CA,EAAQkF,EAAWsC,aAH2BzD,SAI9CA,KACGtG,qDAEH,MAAMmJ,EAAU3L,EAAuB,MACvC0L,EAAcC,GAEd,MAAOrE,EAAM6C,GAAcH,EAAwBC,EAAYlF,EAAO+D,GAEhE9D,EAAgBP,EAAgB,CAAC,iBAAkBL,IAEzD,OACEhC,2BAASI,GAAM6B,IAAKsH,EAASvH,UAAWY,IACtC5C,gBAACiH,GAAW/B,KAAMA,EAAMwB,SAAUqB,IAClC/H,gBAACwG,GAAIC,IAAKvB,EAAKP,EAAG+B,SAAUqB,EAAY/F,UAAU,mCCxBlD6F,EAAiC,CACrCsC,aAAc,MACdlC,OAAQxE,EACR6E,SAAU,EAAG3D,EAAAA,EAAGC,EAAAA,EAAGG,EAAAA,KAAQE,EAAU,CAAEN,EAAAA,EAAGC,EAAAA,EAAGG,EAAAA,EAAGb,EAAG,IACnDiE,MAAOT,GAGI0C,EAAkBC,GAC7BrK,gBAACkK,OAAgBG,GAAOxC,WAAYA,KCEzByC,GAAQ,EAAGtI,UAAAA,EAAWkD,KAAAA,EAAMwB,SAAAA,MACvC,MAcM6D,EAAgB,CACpBC,0CAJgBhF,EAAiBkD,OAAOC,OAAO,GAAIzD,EAAM,CAAEhB,EAAG,SAChDsB,EAAiBkD,OAAOC,OAAO,GAAIzD,EAAM,CAAEhB,EAAG,SAMxDtB,EAAgBP,EAAgB,CAAC,wBAAyBL,IAC1DyI,EAAY1H,EAAe,IAATmC,EAAKhB,GAE7B,OACElE,uBAAKgC,UAAWY,GACd5C,uBAAKgC,UAAU,iCAAiCa,MAAO0H,IACvDvK,gBAACD,GACCG,OAzBcyG,IAClBD,EAAS,CAAExC,EAAGyC,EAAYtH,QAyBtBc,MAtBayG,IAEjBF,EAAS,CAAExC,EAAGlG,EAAMkH,EAAKhB,EAAI0C,EAAOvH,SAqBhCwH,aAAW,QACXM,oBAAmBsD,KACnB3D,gBAAe2D,EACfzD,gBAAc,IACdD,gBAAc,OAEd/G,gBAAC0C,GACCV,UAAU,gCACV3C,KAAM6F,EAAKhB,EACXvB,MAAO6C,EAAiBN,QCvCrBwF,WAAwC1I,UACnDA,EADmD6F,WAEnDA,EAFmDlF,MAGnDA,EAAQkF,EAAWsC,aAHgCzD,SAInDA,KACGtG,qDAEH,MAAMmJ,EAAU3L,EAAuB,MACvC0L,EAAcC,GAEd,MAAOrE,EAAM6C,GAAcH,EAAwBC,EAAYlF,EAAO+D,GAEhE9D,EAAgBP,EAAgB,CAAC,iBAAkBL,IAEzD,OACEhC,2BAASI,GAAM6B,IAAKsH,EAASvH,UAAWY,IACtC5C,gBAACiH,GAAW/B,KAAMA,EAAMwB,SAAUqB,IAClC/H,gBAACwG,GAAIC,IAAKvB,EAAKP,EAAG+B,SAAUqB,IAC5B/H,gBAACsK,IAAMpF,KAAMA,EAAMwB,SAAUqB,EAAY/F,UAAU,mCC1BnD6F,GAAiC,CACrCsC,aAAc,OACdlC,OAAQxE,EACR6E,SAAUrD,EACVkD,MAAOT,GAGIiD,GAAuBN,GAClCrK,gBAAC0K,QAAqBL,GAAOxC,WAAYA,MCRrCA,GAAoC,CACxCsC,aAAc,CAAExF,EAAG,EAAGC,EAAG,EAAGC,EAAG,EAAGX,EAAG,GACrC+D,OAAQvD,EACR4D,SAAUjD,EACV8C,MAAOf,GAGIwD,GAAmBP,GAC9BrK,gBAAC0K,QAAqBL,GAAOxC,WAAYA,MCRrCA,GAAiC,CACrCsC,aAAc,qBACdlC,OAAQ3D,EACRgE,SAAU9C,EACV2C,MAAOX,GAGIqD,GACXR,GACgBrK,gBAAC0K,QAAqBL,GAAOxC,WAAYA,MCTrDA,GAAmC,CACvCsC,aAAc,CAAExF,EAAG,EAAGC,EAAG,EAAGC,EAAG,GAC/BoD,OAAQ,EAAGtD,EAAAA,EAAGC,EAAAA,EAAGC,EAAAA,KAAQH,EAAW,CAAEC,EAAAA,EAAGC,EAAAA,EAAGC,EAAAA,EAAGX,EAAG,IAClDoE,SAAWpD,Gf8LY,GAAGP,EAAAA,EAAGC,EAAAA,EAAGC,EAAAA,OAAgCF,EAAAA,EAAGC,EAAAA,EAAGC,EAAAA,Ie9LlDiG,CAAUzF,EAAWH,IACzCiD,MAAOf,GAGI2D,GAAkBV,GAC7BrK,gBAACkK,OAAgBG,GAAOxC,WAAYA,MCRhCA,GAAiC,CACrCsC,aAAc,iBACdlC,OAAQjD,EACRsD,SAAU/C,EACV4C,MAAOX,GAGIwD,GAAwBX,GACnCrK,gBAACkK,OAAgBG,GAAOxC,WAAYA,MCRhCA,GAAoC,CACxCsC,aAAc,CAAExF,EAAG,EAAGC,EAAG,EAAGG,EAAG,EAAGb,EAAG,GACrC+D,OAAS/C,GAASA,EAClBoD,SAAUvC,EACVoC,MAAOf,GAGI6D,GAAmBZ,GAC9BrK,gBAAC0K,QAAqBL,GAAOxC,WAAYA,MCRrCA,GAAiC,CACrCsC,aAAc,qBACdlC,OAAQpC,EACRyC,SlB+E+BpD,IAC/B,MAAMP,EAAEA,EAAFC,EAAKA,EAALG,EAAQA,EAARb,GAAiB6B,EAAUb,GACjC,cAAeP,MAAMC,OAAOG,OAAOb,MkBhFnCiE,MAAOX,GAGI0D,GACXb,GACgBrK,gBAAC0K,QAAqBL,GAAOxC,WAAYA,MCTrDA,GAAmC,CACvCsC,aAAc,CAAExF,EAAG,EAAGC,EAAG,EAAGG,EAAG,GAC/BkD,OAAQ,EAAGtD,EAAAA,EAAGC,EAAAA,EAAGG,EAAAA,OAAWJ,EAAAA,EAAGC,EAAAA,EAAGG,EAAAA,EAAGb,EAAG,IACxCoE,SnBgMwBpD,IACxB,MAAMP,EAAEA,EAAFC,EAAKA,EAALG,EAAQA,GAAMgB,EAAUb,GAC9B,MAAO,CAAEP,EAAAA,EAAGC,EAAAA,EAAGG,EAAAA,ImBjMfoD,MAAOf,GAGI+D,GAAkBd,GAC7BrK,gBAACkK,OAAgBG,GAAOxC,WAAYA,MCRhCA,GAAiC,CACrCsC,aAAc,iBACdlC,OAAQjC,EACRsC,SpB0E8BpD,IAC9B,MAAMP,EAAEA,EAAFC,EAAKA,EAALG,EAAQA,GAAMgB,EAAUb,GAC9B,aAAcP,MAAMC,OAAOG,OoB3E3BoD,MAAOX,GAGI4D,GAAwBf,GACnCrK,gBAACkK,OAAgBG,GAAOxC,WAAYA,MCRhCA,GAAoC,CACxCsC,aAAc,CAAErG,EAAG,EAAGE,EAAG,EAAGC,EAAG,EAAGC,EAAG,GACrC+D,OAAQtE,EACR2E,SAAUlD,EACV+C,MAAOf,GAGIiE,GAAmBhB,GAC9BrK,gBAAC0K,QAAqBL,GAAOxC,WAAYA,MCRrCA,GAAiC,CACrCsC,aAAc,mBACdlC,OAAQhC,EACRqC,StBiH+BpD,IAC/B,MAAMpB,EAAAE,EAAKA,EAALC,EAAQA,EAARC,GAAiBkB,EAAWF,GAClC,cAAepB,MAAME,MAAMC,MAAMC,MsBlHjCiE,MAAOX,GAGI8D,GACXjB,GACgBrK,gBAAC0K,QAAqBL,GAAOxC,WAAYA,MCTrDA,GAAmC,CACvCsC,aAAc,CAAErG,EAAG,EAAGE,EAAG,EAAGC,EAAG,GAC/BgE,OAAQ,EAAGnE,EAAAA,EAAGE,EAAAA,EAAGC,EAAAA,KAAQN,EAAW,CAAEG,EAAAA,EAAGE,EAAAA,EAAGC,EAAAA,EAAGC,EAAG,IAClDoE,SAAWpD,GvB4LY,GAAGpB,EAAAA,EAAGE,EAAAA,EAAGC,EAAAA,OAAgCH,EAAAA,EAAGE,EAAAA,EAAGC,EAAAA,IuB5LlDsH,CAAUnG,EAAWF,IACzCiD,MAAOf,GAGIoE,GAAkBnB,GAC7BrK,gBAACkK,OAAgBG,GAAOxC,WAAYA,MCRhCA,GAAiC,CACrCsC,aAAc,eACdlC,OAAQ9B,EACRmC,SxB4G8BpD,IAC9B,MAAMpB,EAAAE,EAAKA,EAALC,EAAQA,GAAMmB,EAAWF,GAC/B,aAAcpB,MAAME,MAAMC,MwB7G1BkE,MAAOX,GAGIiE,GAAwBpB,GACnCrK,gBAACkK,OAAgBG,GAAOxC,WAAYA,MCfhC6D,GAAU,uBCgBHC,GAActB,IACzB,MAAM1H,MAAEA,EAAQ,GAAV+D,SAAcA,EAAdkF,OAAwBA,EAAxBC,OAAgCA,EAAhCC,SAAwCA,EAAxC1F,OAAkDA,EAAlD2F,QAA0DA,GAAqB1B,EAATjK,IAASiK,yEAC9EvM,EAAOkO,GAAYhE,EAAS,IAAM6D,EAAOlJ,IAC1CmF,EAAmBrK,EAAyBiJ,GAC5CuF,EAAiBxO,EAAqDmO,GAGtErD,EAAeC,EAClB0D,IACC,MAAMC,EAAaN,EAAOK,EAAEE,OAAOtO,OACnCkO,EAASG,GACLL,EAASK,IAAarE,EAAiBiE,EAAUA,EAAQI,GAAcA,IAE7E,CAACN,EAAQE,EAASD,EAAUhE,IAIxBuE,EAAa7D,EAChB0D,IACMJ,EAASI,EAAEE,OAAOtO,QAAQkO,EAASH,EAAOlJ,IAC/CsJ,EAAeC,IAEjB,CAACvJ,EAAOkJ,EAAQC,EAAUG,IAQ5B,OAJApK,EAAU,KACRmK,EAASH,EAAOlJ,KACf,CAACA,EAAOkJ,IAGT7L,6BACMI,GACJtC,MAAOsI,EAASA,EAAOtI,GAASA,EAChCwO,WAAW,QACX5F,SAAU6B,EACVqD,OAAQS,MCtCRE,GAAUzO,GAAkB,IAAMA,EAE3B0O,GAAiBnC,IAC5B,MAAMoC,SAAEA,EAAFC,MAAYA,GAAmBrC,EAATjK,IAASiK,wBAG/BwB,EAASrD,EACZ1K,GAAkBA,EAAM2J,QAAQ,iBAAkB,IAAI5D,UAAU,EAAG6I,EAAQ,EAAI,GAChF,CAACA,IAIGZ,EAAWtD,EAAa1K,GFxBR,EAACA,EAAe4O,KACtC,MAAMlI,EAAQkH,GAAQjH,KAAK3G,GACrBoB,EAASsF,EAAQA,EAAM,GAAGtF,OAAS,EAEzC,OACa,IAAXA,GACW,IAAXA,KACGwN,GAAoB,IAAXxN,KACTwN,GAAoB,IAAXxN,GEgBkCyN,CAAS7O,EAAO4O,GAAQ,CAACA,IAEzE,OACE1M,gBAAC2L,QACKvL,GACJyL,OAAQA,EACRzF,OAAQqG,EAAWF,QAASzH,EAC5BiH,QAASQ,GACTT,SAAUA"}