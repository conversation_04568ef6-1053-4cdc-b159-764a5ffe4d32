function rg(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var Lx=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ld(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Dd={exports:{}},el={},Id={exports:{}},Q={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ro=Symbol.for("react.element"),og=Symbol.for("react.portal"),ig=Symbol.for("react.fragment"),lg=Symbol.for("react.strict_mode"),sg=Symbol.for("react.profiler"),ag=Symbol.for("react.provider"),ug=Symbol.for("react.context"),cg=Symbol.for("react.forward_ref"),dg=Symbol.for("react.suspense"),fg=Symbol.for("react.memo"),pg=Symbol.for("react.lazy"),Bu=Symbol.iterator;function hg(e){return e===null||typeof e!="object"?null:(e=Bu&&e[Bu]||e["@@iterator"],typeof e=="function"?e:null)}var zd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Md=Object.assign,Fd={};function xr(e,t,n){this.props=e,this.context=t,this.refs=Fd,this.updater=n||zd}xr.prototype.isReactComponent={};xr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};xr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function jd(){}jd.prototype=xr.prototype;function ka(e,t,n){this.props=e,this.context=t,this.refs=Fd,this.updater=n||zd}var ba=ka.prototype=new jd;ba.constructor=ka;Md(ba,xr.prototype);ba.isPureReactComponent=!0;var $u=Array.isArray,Ud=Object.prototype.hasOwnProperty,Pa={current:null},Bd={key:!0,ref:!0,__self:!0,__source:!0};function $d(e,t,n){var r,o={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)Ud.call(t,r)&&!Bd.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:Ro,type:e,key:i,ref:l,props:o,_owner:Pa.current}}function mg(e,t){return{$$typeof:Ro,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ra(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ro}function gg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Vu=/\/+/g;function Rl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?gg(""+e.key):t.toString(36)}function li(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case Ro:case og:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+Rl(l,0):r,$u(o)?(n="",e!=null&&(n=e.replace(Vu,"$&/")+"/"),li(o,t,n,"",function(u){return u})):o!=null&&(Ra(o)&&(o=mg(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(Vu,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",$u(e))for(var s=0;s<e.length;s++){i=e[s];var a=r+Rl(i,s);l+=li(i,t,n,a,o)}else if(a=hg(e),typeof a=="function")for(e=a.call(e),s=0;!(i=e.next()).done;)i=i.value,a=r+Rl(i,s++),l+=li(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function zo(e,t,n){if(e==null)return e;var r=[],o=0;return li(e,r,"","",function(i){return t.call(n,i,o++)}),r}function vg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Oe={current:null},si={transition:null},yg={ReactCurrentDispatcher:Oe,ReactCurrentBatchConfig:si,ReactCurrentOwner:Pa};function Vd(){throw Error("act(...) is not supported in production builds of React.")}Q.Children={map:zo,forEach:function(e,t,n){zo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return zo(e,function(){t++}),t},toArray:function(e){return zo(e,function(t){return t})||[]},only:function(e){if(!Ra(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Q.Component=xr;Q.Fragment=ig;Q.Profiler=sg;Q.PureComponent=ka;Q.StrictMode=lg;Q.Suspense=dg;Q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=yg;Q.act=Vd;Q.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Md({},e.props),o=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=Pa.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)Ud.call(t,a)&&!Bd.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:Ro,type:e.type,key:o,ref:i,props:r,_owner:l}};Q.createContext=function(e){return e={$$typeof:ug,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:ag,_context:e},e.Consumer=e};Q.createElement=$d;Q.createFactory=function(e){var t=$d.bind(null,e);return t.type=e,t};Q.createRef=function(){return{current:null}};Q.forwardRef=function(e){return{$$typeof:cg,render:e}};Q.isValidElement=Ra;Q.lazy=function(e){return{$$typeof:pg,_payload:{_status:-1,_result:e},_init:vg}};Q.memo=function(e,t){return{$$typeof:fg,type:e,compare:t===void 0?null:t}};Q.startTransition=function(e){var t=si.transition;si.transition={};try{e()}finally{si.transition=t}};Q.unstable_act=Vd;Q.useCallback=function(e,t){return Oe.current.useCallback(e,t)};Q.useContext=function(e){return Oe.current.useContext(e)};Q.useDebugValue=function(){};Q.useDeferredValue=function(e){return Oe.current.useDeferredValue(e)};Q.useEffect=function(e,t){return Oe.current.useEffect(e,t)};Q.useId=function(){return Oe.current.useId()};Q.useImperativeHandle=function(e,t,n){return Oe.current.useImperativeHandle(e,t,n)};Q.useInsertionEffect=function(e,t){return Oe.current.useInsertionEffect(e,t)};Q.useLayoutEffect=function(e,t){return Oe.current.useLayoutEffect(e,t)};Q.useMemo=function(e,t){return Oe.current.useMemo(e,t)};Q.useReducer=function(e,t,n){return Oe.current.useReducer(e,t,n)};Q.useRef=function(e){return Oe.current.useRef(e)};Q.useState=function(e){return Oe.current.useState(e)};Q.useSyncExternalStore=function(e,t,n){return Oe.current.useSyncExternalStore(e,t,n)};Q.useTransition=function(){return Oe.current.useTransition()};Q.version="18.3.1";Id.exports=Q;var g=Id.exports;const Nt=Ld(g),Hd=rg({__proto__:null,default:Nt},[g]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wg=g,Sg=Symbol.for("react.element"),xg=Symbol.for("react.fragment"),_g=Object.prototype.hasOwnProperty,Cg=wg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Eg={key:!0,ref:!0,__self:!0,__source:!0};function Wd(e,t,n){var r,o={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)_g.call(t,r)&&!Eg.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Sg,type:e,key:i,ref:l,props:o,_owner:Cg.current}}el.Fragment=xg;el.jsx=Wd;el.jsxs=Wd;Dd.exports=el;var A=Dd.exports,Hu={},Kd={exports:{}},Ye={},Gd={exports:{}},Qd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(b,I){var F=b.length;b.push(I);e:for(;0<F;){var B=F-1>>>1,q=b[B];if(0<o(q,I))b[B]=I,b[F]=q,F=B;else break e}}function n(b){return b.length===0?null:b[0]}function r(b){if(b.length===0)return null;var I=b[0],F=b.pop();if(F!==I){b[0]=F;e:for(var B=0,q=b.length,Ee=q>>>1;B<Ee;){var ve=2*(B+1)-1,Ze=b[ve],he=ve+1,V=b[he];if(0>o(Ze,F))he<q&&0>o(V,Ze)?(b[B]=V,b[he]=F,B=he):(b[B]=Ze,b[ve]=F,B=ve);else if(he<q&&0>o(V,F))b[B]=V,b[he]=F,B=he;else break e}}return I}function o(b,I){var F=b.sortIndex-I.sortIndex;return F!==0?F:b.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],d=1,m=null,h=3,y=!1,w=!1,f=!1,S=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(b){for(var I=n(u);I!==null;){if(I.callback===null)r(u);else if(I.startTime<=b)r(u),I.sortIndex=I.expirationTime,t(a,I);else break;I=n(u)}}function x(b){if(f=!1,v(b),!w)if(n(a)!==null)w=!0,j(C);else{var I=n(u);I!==null&&$(x,I.startTime-b)}}function C(b,I){w=!1,f&&(f=!1,p(P),P=-1),y=!0;var F=h;try{for(v(I),m=n(a);m!==null&&(!(m.expirationTime>I)||b&&!O());){var B=m.callback;if(typeof B=="function"){m.callback=null,h=m.priorityLevel;var q=B(m.expirationTime<=I);I=e.unstable_now(),typeof q=="function"?m.callback=q:m===n(a)&&r(a),v(I)}else r(a);m=n(a)}if(m!==null)var Ee=!0;else{var ve=n(u);ve!==null&&$(x,ve.startTime-I),Ee=!1}return Ee}finally{m=null,h=F,y=!1}}var R=!1,_=null,P=-1,z=5,E=-1;function O(){return!(e.unstable_now()-E<z)}function N(){if(_!==null){var b=e.unstable_now();E=b;var I=!0;try{I=_(!0,b)}finally{I?D():(R=!1,_=null)}}else R=!1}var D;if(typeof c=="function")D=function(){c(N)};else if(typeof MessageChannel<"u"){var k=new MessageChannel,U=k.port2;k.port1.onmessage=N,D=function(){U.postMessage(null)}}else D=function(){S(N,0)};function j(b){_=b,R||(R=!0,D())}function $(b,I){P=S(function(){b(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(b){b.callback=null},e.unstable_continueExecution=function(){w||y||(w=!0,j(C))},e.unstable_forceFrameRate=function(b){0>b||125<b?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<b?Math.floor(1e3/b):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(b){switch(h){case 1:case 2:case 3:var I=3;break;default:I=h}var F=h;h=I;try{return b()}finally{h=F}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(b,I){switch(b){case 1:case 2:case 3:case 4:case 5:break;default:b=3}var F=h;h=b;try{return I()}finally{h=F}},e.unstable_scheduleCallback=function(b,I,F){var B=e.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?B+F:B):F=B,b){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=F+q,b={id:d++,callback:I,priorityLevel:b,startTime:F,expirationTime:q,sortIndex:-1},F>B?(b.sortIndex=F,t(u,b),n(a)===null&&b===n(u)&&(f?(p(P),P=-1):f=!0,$(x,F-B))):(b.sortIndex=q,t(a,b),w||y||(w=!0,j(C))),b},e.unstable_shouldYield=O,e.unstable_wrapCallback=function(b){var I=h;return function(){var F=h;h=I;try{return b.apply(this,arguments)}finally{h=F}}}})(Qd);Gd.exports=Qd;var kg=Gd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bg=g,Qe=kg;function L(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Yd=new Set,lo={};function Dn(e,t){fr(e,t),fr(e+"Capture",t)}function fr(e,t){for(lo[e]=t,e=0;e<t.length;e++)Yd.add(t[e])}var It=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vs=Object.prototype.hasOwnProperty,Pg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Wu={},Ku={};function Rg(e){return vs.call(Ku,e)?!0:vs.call(Wu,e)?!1:Pg.test(e)?Ku[e]=!0:(Wu[e]=!0,!1)}function Ng(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ag(e,t,n,r){if(t===null||typeof t>"u"||Ng(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Le(e,t,n,r,o,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var Ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ce[e]=new Le(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ce[t]=new Le(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ce[e]=new Le(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ce[e]=new Le(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ce[e]=new Le(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ce[e]=new Le(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ce[e]=new Le(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ce[e]=new Le(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ce[e]=new Le(e,5,!1,e.toLowerCase(),null,!1,!1)});var Na=/[\-:]([a-z])/g;function Aa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Na,Aa);Ce[t]=new Le(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Na,Aa);Ce[t]=new Le(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Na,Aa);Ce[t]=new Le(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ce[e]=new Le(e,1,!1,e.toLowerCase(),null,!1,!1)});Ce.xlinkHref=new Le("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ce[e]=new Le(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ta(e,t,n,r){var o=Ce.hasOwnProperty(t)?Ce[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ag(t,n,o,r)&&(n=null),r||o===null?Rg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Bt=bg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Mo=Symbol.for("react.element"),$n=Symbol.for("react.portal"),Vn=Symbol.for("react.fragment"),Oa=Symbol.for("react.strict_mode"),ys=Symbol.for("react.profiler"),Xd=Symbol.for("react.provider"),Zd=Symbol.for("react.context"),La=Symbol.for("react.forward_ref"),ws=Symbol.for("react.suspense"),Ss=Symbol.for("react.suspense_list"),Da=Symbol.for("react.memo"),Kt=Symbol.for("react.lazy"),Jd=Symbol.for("react.offscreen"),Gu=Symbol.iterator;function Or(e){return e===null||typeof e!="object"?null:(e=Gu&&e[Gu]||e["@@iterator"],typeof e=="function"?e:null)}var ae=Object.assign,Nl;function Vr(e){if(Nl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Nl=t&&t[1]||""}return`
`+Nl+e}var Al=!1;function Tl(e,t){if(!e||Al)return"";Al=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),l=o.length-1,s=i.length-1;1<=l&&0<=s&&o[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(o[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||o[l]!==i[s]){var a=`
`+o[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{Al=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Vr(e):""}function Tg(e){switch(e.tag){case 5:return Vr(e.type);case 16:return Vr("Lazy");case 13:return Vr("Suspense");case 19:return Vr("SuspenseList");case 0:case 2:case 15:return e=Tl(e.type,!1),e;case 11:return e=Tl(e.type.render,!1),e;case 1:return e=Tl(e.type,!0),e;default:return""}}function xs(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Vn:return"Fragment";case $n:return"Portal";case ys:return"Profiler";case Oa:return"StrictMode";case ws:return"Suspense";case Ss:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Zd:return(e.displayName||"Context")+".Consumer";case Xd:return(e._context.displayName||"Context")+".Provider";case La:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Da:return t=e.displayName||null,t!==null?t:xs(e.type)||"Memo";case Kt:t=e._payload,e=e._init;try{return xs(e(t))}catch{}}return null}function Og(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return xs(t);case 8:return t===Oa?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function an(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function qd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Lg(e){var t=qd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Fo(e){e._valueTracker||(e._valueTracker=Lg(e))}function ef(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=qd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ei(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function _s(e,t){var n=t.checked;return ae({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Qu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=an(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function tf(e,t){t=t.checked,t!=null&&Ta(e,"checked",t,!1)}function Cs(e,t){tf(e,t);var n=an(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Es(e,t.type,n):t.hasOwnProperty("defaultValue")&&Es(e,t.type,an(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Yu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Es(e,t,n){(t!=="number"||Ei(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Hr=Array.isArray;function nr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+an(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ks(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(L(91));return ae({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Xu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(L(92));if(Hr(n)){if(1<n.length)throw Error(L(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:an(n)}}function nf(e,t){var n=an(t.value),r=an(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Zu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function rf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function bs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?rf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var jo,of=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(jo=jo||document.createElement("div"),jo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=jo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function so(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Qr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Dg=["Webkit","ms","Moz","O"];Object.keys(Qr).forEach(function(e){Dg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Qr[t]=Qr[e]})});function lf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Qr.hasOwnProperty(e)&&Qr[e]?(""+t).trim():t+"px"}function sf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=lf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Ig=ae({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ps(e,t){if(t){if(Ig[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(L(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(L(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(L(61))}if(t.style!=null&&typeof t.style!="object")throw Error(L(62))}}function Rs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ns=null;function Ia(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var As=null,rr=null,or=null;function Ju(e){if(e=To(e)){if(typeof As!="function")throw Error(L(280));var t=e.stateNode;t&&(t=il(t),As(e.stateNode,e.type,t))}}function af(e){rr?or?or.push(e):or=[e]:rr=e}function uf(){if(rr){var e=rr,t=or;if(or=rr=null,Ju(e),t)for(e=0;e<t.length;e++)Ju(t[e])}}function cf(e,t){return e(t)}function df(){}var Ol=!1;function ff(e,t,n){if(Ol)return e(t,n);Ol=!0;try{return cf(e,t,n)}finally{Ol=!1,(rr!==null||or!==null)&&(df(),uf())}}function ao(e,t){var n=e.stateNode;if(n===null)return null;var r=il(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(L(231,t,typeof n));return n}var Ts=!1;if(It)try{var Lr={};Object.defineProperty(Lr,"passive",{get:function(){Ts=!0}}),window.addEventListener("test",Lr,Lr),window.removeEventListener("test",Lr,Lr)}catch{Ts=!1}function zg(e,t,n,r,o,i,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Yr=!1,ki=null,bi=!1,Os=null,Mg={onError:function(e){Yr=!0,ki=e}};function Fg(e,t,n,r,o,i,l,s,a){Yr=!1,ki=null,zg.apply(Mg,arguments)}function jg(e,t,n,r,o,i,l,s,a){if(Fg.apply(this,arguments),Yr){if(Yr){var u=ki;Yr=!1,ki=null}else throw Error(L(198));bi||(bi=!0,Os=u)}}function In(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function pf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function qu(e){if(In(e)!==e)throw Error(L(188))}function Ug(e){var t=e.alternate;if(!t){if(t=In(e),t===null)throw Error(L(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return qu(o),e;if(i===r)return qu(o),t;i=i.sibling}throw Error(L(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(L(189))}}if(n.alternate!==r)throw Error(L(190))}if(n.tag!==3)throw Error(L(188));return n.stateNode.current===n?e:t}function hf(e){return e=Ug(e),e!==null?mf(e):null}function mf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=mf(e);if(t!==null)return t;e=e.sibling}return null}var gf=Qe.unstable_scheduleCallback,ec=Qe.unstable_cancelCallback,Bg=Qe.unstable_shouldYield,$g=Qe.unstable_requestPaint,de=Qe.unstable_now,Vg=Qe.unstable_getCurrentPriorityLevel,za=Qe.unstable_ImmediatePriority,vf=Qe.unstable_UserBlockingPriority,Pi=Qe.unstable_NormalPriority,Hg=Qe.unstable_LowPriority,yf=Qe.unstable_IdlePriority,tl=null,_t=null;function Wg(e){if(_t&&typeof _t.onCommitFiberRoot=="function")try{_t.onCommitFiberRoot(tl,e,void 0,(e.current.flags&128)===128)}catch{}}var ft=Math.clz32?Math.clz32:Qg,Kg=Math.log,Gg=Math.LN2;function Qg(e){return e>>>=0,e===0?32:31-(Kg(e)/Gg|0)|0}var Uo=64,Bo=4194304;function Wr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ri(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~o;s!==0?r=Wr(s):(i&=l,i!==0&&(r=Wr(i)))}else l=n&~o,l!==0?r=Wr(l):i!==0&&(r=Wr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ft(t),o=1<<n,r|=e[n],t&=~o;return r}function Yg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Xg(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-ft(i),s=1<<l,a=o[l];a===-1?(!(s&n)||s&r)&&(o[l]=Yg(s,t)):a<=t&&(e.expiredLanes|=s),i&=~s}}function Ls(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function wf(){var e=Uo;return Uo<<=1,!(Uo&4194240)&&(Uo=64),e}function Ll(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function No(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ft(t),e[t]=n}function Zg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-ft(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Ma(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ft(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var Z=0;function Sf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var xf,Fa,_f,Cf,Ef,Ds=!1,$o=[],qt=null,en=null,tn=null,uo=new Map,co=new Map,Yt=[],Jg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function tc(e,t){switch(e){case"focusin":case"focusout":qt=null;break;case"dragenter":case"dragleave":en=null;break;case"mouseover":case"mouseout":tn=null;break;case"pointerover":case"pointerout":uo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":co.delete(t.pointerId)}}function Dr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=To(t),t!==null&&Fa(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function qg(e,t,n,r,o){switch(t){case"focusin":return qt=Dr(qt,e,t,n,r,o),!0;case"dragenter":return en=Dr(en,e,t,n,r,o),!0;case"mouseover":return tn=Dr(tn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return uo.set(i,Dr(uo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,co.set(i,Dr(co.get(i)||null,e,t,n,r,o)),!0}return!1}function kf(e){var t=Sn(e.target);if(t!==null){var n=In(t);if(n!==null){if(t=n.tag,t===13){if(t=pf(n),t!==null){e.blockedOn=t,Ef(e.priority,function(){_f(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ai(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Is(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ns=r,n.target.dispatchEvent(r),Ns=null}else return t=To(n),t!==null&&Fa(t),e.blockedOn=n,!1;t.shift()}return!0}function nc(e,t,n){ai(e)&&n.delete(t)}function ev(){Ds=!1,qt!==null&&ai(qt)&&(qt=null),en!==null&&ai(en)&&(en=null),tn!==null&&ai(tn)&&(tn=null),uo.forEach(nc),co.forEach(nc)}function Ir(e,t){e.blockedOn===t&&(e.blockedOn=null,Ds||(Ds=!0,Qe.unstable_scheduleCallback(Qe.unstable_NormalPriority,ev)))}function fo(e){function t(o){return Ir(o,e)}if(0<$o.length){Ir($o[0],e);for(var n=1;n<$o.length;n++){var r=$o[n];r.blockedOn===e&&(r.blockedOn=null)}}for(qt!==null&&Ir(qt,e),en!==null&&Ir(en,e),tn!==null&&Ir(tn,e),uo.forEach(t),co.forEach(t),n=0;n<Yt.length;n++)r=Yt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Yt.length&&(n=Yt[0],n.blockedOn===null);)kf(n),n.blockedOn===null&&Yt.shift()}var ir=Bt.ReactCurrentBatchConfig,Ni=!0;function tv(e,t,n,r){var o=Z,i=ir.transition;ir.transition=null;try{Z=1,ja(e,t,n,r)}finally{Z=o,ir.transition=i}}function nv(e,t,n,r){var o=Z,i=ir.transition;ir.transition=null;try{Z=4,ja(e,t,n,r)}finally{Z=o,ir.transition=i}}function ja(e,t,n,r){if(Ni){var o=Is(e,t,n,r);if(o===null)Vl(e,t,r,Ai,n),tc(e,r);else if(qg(o,e,t,n,r))r.stopPropagation();else if(tc(e,r),t&4&&-1<Jg.indexOf(e)){for(;o!==null;){var i=To(o);if(i!==null&&xf(i),i=Is(e,t,n,r),i===null&&Vl(e,t,r,Ai,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Vl(e,t,r,null,n)}}var Ai=null;function Is(e,t,n,r){if(Ai=null,e=Ia(r),e=Sn(e),e!==null)if(t=In(e),t===null)e=null;else if(n=t.tag,n===13){if(e=pf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ai=e,null}function bf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Vg()){case za:return 1;case vf:return 4;case Pi:case Hg:return 16;case yf:return 536870912;default:return 16}default:return 16}}var Zt=null,Ua=null,ui=null;function Pf(){if(ui)return ui;var e,t=Ua,n=t.length,r,o="value"in Zt?Zt.value:Zt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[i-r];r++);return ui=o.slice(e,1<r?1-r:void 0)}function ci(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Vo(){return!0}function rc(){return!1}function Xe(e){function t(n,r,o,i,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Vo:rc,this.isPropagationStopped=rc,this}return ae(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Vo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Vo)},persist:function(){},isPersistent:Vo}),t}var _r={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ba=Xe(_r),Ao=ae({},_r,{view:0,detail:0}),rv=Xe(Ao),Dl,Il,zr,nl=ae({},Ao,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$a,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==zr&&(zr&&e.type==="mousemove"?(Dl=e.screenX-zr.screenX,Il=e.screenY-zr.screenY):Il=Dl=0,zr=e),Dl)},movementY:function(e){return"movementY"in e?e.movementY:Il}}),oc=Xe(nl),ov=ae({},nl,{dataTransfer:0}),iv=Xe(ov),lv=ae({},Ao,{relatedTarget:0}),zl=Xe(lv),sv=ae({},_r,{animationName:0,elapsedTime:0,pseudoElement:0}),av=Xe(sv),uv=ae({},_r,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),cv=Xe(uv),dv=ae({},_r,{data:0}),ic=Xe(dv),fv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},pv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function mv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=hv[e])?!!t[e]:!1}function $a(){return mv}var gv=ae({},Ao,{key:function(e){if(e.key){var t=fv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ci(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?pv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$a,charCode:function(e){return e.type==="keypress"?ci(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ci(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),vv=Xe(gv),yv=ae({},nl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),lc=Xe(yv),wv=ae({},Ao,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$a}),Sv=Xe(wv),xv=ae({},_r,{propertyName:0,elapsedTime:0,pseudoElement:0}),_v=Xe(xv),Cv=ae({},nl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ev=Xe(Cv),kv=[9,13,27,32],Va=It&&"CompositionEvent"in window,Xr=null;It&&"documentMode"in document&&(Xr=document.documentMode);var bv=It&&"TextEvent"in window&&!Xr,Rf=It&&(!Va||Xr&&8<Xr&&11>=Xr),sc=" ",ac=!1;function Nf(e,t){switch(e){case"keyup":return kv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Af(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Hn=!1;function Pv(e,t){switch(e){case"compositionend":return Af(t);case"keypress":return t.which!==32?null:(ac=!0,sc);case"textInput":return e=t.data,e===sc&&ac?null:e;default:return null}}function Rv(e,t){if(Hn)return e==="compositionend"||!Va&&Nf(e,t)?(e=Pf(),ui=Ua=Zt=null,Hn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Rf&&t.locale!=="ko"?null:t.data;default:return null}}var Nv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function uc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Nv[e.type]:t==="textarea"}function Tf(e,t,n,r){af(r),t=Ti(t,"onChange"),0<t.length&&(n=new Ba("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Zr=null,po=null;function Av(e){$f(e,0)}function rl(e){var t=Gn(e);if(ef(t))return e}function Tv(e,t){if(e==="change")return t}var Of=!1;if(It){var Ml;if(It){var Fl="oninput"in document;if(!Fl){var cc=document.createElement("div");cc.setAttribute("oninput","return;"),Fl=typeof cc.oninput=="function"}Ml=Fl}else Ml=!1;Of=Ml&&(!document.documentMode||9<document.documentMode)}function dc(){Zr&&(Zr.detachEvent("onpropertychange",Lf),po=Zr=null)}function Lf(e){if(e.propertyName==="value"&&rl(po)){var t=[];Tf(t,po,e,Ia(e)),ff(Av,t)}}function Ov(e,t,n){e==="focusin"?(dc(),Zr=t,po=n,Zr.attachEvent("onpropertychange",Lf)):e==="focusout"&&dc()}function Lv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return rl(po)}function Dv(e,t){if(e==="click")return rl(t)}function Iv(e,t){if(e==="input"||e==="change")return rl(t)}function zv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ht=typeof Object.is=="function"?Object.is:zv;function ho(e,t){if(ht(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!vs.call(t,o)||!ht(e[o],t[o]))return!1}return!0}function fc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function pc(e,t){var n=fc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=fc(n)}}function Df(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Df(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function If(){for(var e=window,t=Ei();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ei(e.document)}return t}function Ha(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Mv(e){var t=If(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Df(n.ownerDocument.documentElement,n)){if(r!==null&&Ha(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=pc(n,i);var l=pc(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Fv=It&&"documentMode"in document&&11>=document.documentMode,Wn=null,zs=null,Jr=null,Ms=!1;function hc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ms||Wn==null||Wn!==Ei(r)||(r=Wn,"selectionStart"in r&&Ha(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Jr&&ho(Jr,r)||(Jr=r,r=Ti(zs,"onSelect"),0<r.length&&(t=new Ba("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Wn)))}function Ho(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Kn={animationend:Ho("Animation","AnimationEnd"),animationiteration:Ho("Animation","AnimationIteration"),animationstart:Ho("Animation","AnimationStart"),transitionend:Ho("Transition","TransitionEnd")},jl={},zf={};It&&(zf=document.createElement("div").style,"AnimationEvent"in window||(delete Kn.animationend.animation,delete Kn.animationiteration.animation,delete Kn.animationstart.animation),"TransitionEvent"in window||delete Kn.transitionend.transition);function ol(e){if(jl[e])return jl[e];if(!Kn[e])return e;var t=Kn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in zf)return jl[e]=t[n];return e}var Mf=ol("animationend"),Ff=ol("animationiteration"),jf=ol("animationstart"),Uf=ol("transitionend"),Bf=new Map,mc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function pn(e,t){Bf.set(e,t),Dn(t,[e])}for(var Ul=0;Ul<mc.length;Ul++){var Bl=mc[Ul],jv=Bl.toLowerCase(),Uv=Bl[0].toUpperCase()+Bl.slice(1);pn(jv,"on"+Uv)}pn(Mf,"onAnimationEnd");pn(Ff,"onAnimationIteration");pn(jf,"onAnimationStart");pn("dblclick","onDoubleClick");pn("focusin","onFocus");pn("focusout","onBlur");pn(Uf,"onTransitionEnd");fr("onMouseEnter",["mouseout","mouseover"]);fr("onMouseLeave",["mouseout","mouseover"]);fr("onPointerEnter",["pointerout","pointerover"]);fr("onPointerLeave",["pointerout","pointerover"]);Dn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Dn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Dn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Dn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Bv=new Set("cancel close invalid load scroll toggle".split(" ").concat(Kr));function gc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,jg(r,t,void 0,e),e.currentTarget=null}function $f(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==i&&o.isPropagationStopped())break e;gc(o,s,u),i=a}else for(l=0;l<r.length;l++){if(s=r[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==i&&o.isPropagationStopped())break e;gc(o,s,u),i=a}}}if(bi)throw e=Os,bi=!1,Os=null,e}function re(e,t){var n=t[$s];n===void 0&&(n=t[$s]=new Set);var r=e+"__bubble";n.has(r)||(Vf(t,e,2,!1),n.add(r))}function $l(e,t,n){var r=0;t&&(r|=4),Vf(n,e,r,t)}var Wo="_reactListening"+Math.random().toString(36).slice(2);function mo(e){if(!e[Wo]){e[Wo]=!0,Yd.forEach(function(n){n!=="selectionchange"&&(Bv.has(n)||$l(n,!1,e),$l(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Wo]||(t[Wo]=!0,$l("selectionchange",!1,t))}}function Vf(e,t,n,r){switch(bf(t)){case 1:var o=tv;break;case 4:o=nv;break;default:o=ja}n=o.bind(null,t,n,e),o=void 0,!Ts||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Vl(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;l=l.return}for(;s!==null;){if(l=Sn(s),l===null)return;if(a=l.tag,a===5||a===6){r=i=l;continue e}s=s.parentNode}}r=r.return}ff(function(){var u=i,d=Ia(n),m=[];e:{var h=Bf.get(e);if(h!==void 0){var y=Ba,w=e;switch(e){case"keypress":if(ci(n)===0)break e;case"keydown":case"keyup":y=vv;break;case"focusin":w="focus",y=zl;break;case"focusout":w="blur",y=zl;break;case"beforeblur":case"afterblur":y=zl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=oc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=iv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Sv;break;case Mf:case Ff:case jf:y=av;break;case Uf:y=_v;break;case"scroll":y=rv;break;case"wheel":y=Ev;break;case"copy":case"cut":case"paste":y=cv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=lc}var f=(t&4)!==0,S=!f&&e==="scroll",p=f?h!==null?h+"Capture":null:h;f=[];for(var c=u,v;c!==null;){v=c;var x=v.stateNode;if(v.tag===5&&x!==null&&(v=x,p!==null&&(x=ao(c,p),x!=null&&f.push(go(c,x,v)))),S)break;c=c.return}0<f.length&&(h=new y(h,w,null,n,d),m.push({event:h,listeners:f}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",h&&n!==Ns&&(w=n.relatedTarget||n.fromElement)&&(Sn(w)||w[zt]))break e;if((y||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,y?(w=n.relatedTarget||n.toElement,y=u,w=w?Sn(w):null,w!==null&&(S=In(w),w!==S||w.tag!==5&&w.tag!==6)&&(w=null)):(y=null,w=u),y!==w)){if(f=oc,x="onMouseLeave",p="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(f=lc,x="onPointerLeave",p="onPointerEnter",c="pointer"),S=y==null?h:Gn(y),v=w==null?h:Gn(w),h=new f(x,c+"leave",y,n,d),h.target=S,h.relatedTarget=v,x=null,Sn(d)===u&&(f=new f(p,c+"enter",w,n,d),f.target=v,f.relatedTarget=S,x=f),S=x,y&&w)t:{for(f=y,p=w,c=0,v=f;v;v=zn(v))c++;for(v=0,x=p;x;x=zn(x))v++;for(;0<c-v;)f=zn(f),c--;for(;0<v-c;)p=zn(p),v--;for(;c--;){if(f===p||p!==null&&f===p.alternate)break t;f=zn(f),p=zn(p)}f=null}else f=null;y!==null&&vc(m,h,y,f,!1),w!==null&&S!==null&&vc(m,S,w,f,!0)}}e:{if(h=u?Gn(u):window,y=h.nodeName&&h.nodeName.toLowerCase(),y==="select"||y==="input"&&h.type==="file")var C=Tv;else if(uc(h))if(Of)C=Iv;else{C=Lv;var R=Ov}else(y=h.nodeName)&&y.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(C=Dv);if(C&&(C=C(e,u))){Tf(m,C,n,d);break e}R&&R(e,h,u),e==="focusout"&&(R=h._wrapperState)&&R.controlled&&h.type==="number"&&Es(h,"number",h.value)}switch(R=u?Gn(u):window,e){case"focusin":(uc(R)||R.contentEditable==="true")&&(Wn=R,zs=u,Jr=null);break;case"focusout":Jr=zs=Wn=null;break;case"mousedown":Ms=!0;break;case"contextmenu":case"mouseup":case"dragend":Ms=!1,hc(m,n,d);break;case"selectionchange":if(Fv)break;case"keydown":case"keyup":hc(m,n,d)}var _;if(Va)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Hn?Nf(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Rf&&n.locale!=="ko"&&(Hn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Hn&&(_=Pf()):(Zt=d,Ua="value"in Zt?Zt.value:Zt.textContent,Hn=!0)),R=Ti(u,P),0<R.length&&(P=new ic(P,e,null,n,d),m.push({event:P,listeners:R}),_?P.data=_:(_=Af(n),_!==null&&(P.data=_)))),(_=bv?Pv(e,n):Rv(e,n))&&(u=Ti(u,"onBeforeInput"),0<u.length&&(d=new ic("onBeforeInput","beforeinput",null,n,d),m.push({event:d,listeners:u}),d.data=_))}$f(m,t)})}function go(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ti(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=ao(e,n),i!=null&&r.unshift(go(e,i,o)),i=ao(e,t),i!=null&&r.push(go(e,i,o))),e=e.return}return r}function zn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function vc(e,t,n,r,o){for(var i=t._reactName,l=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,o?(a=ao(n,i),a!=null&&l.unshift(go(n,a,s))):o||(a=ao(n,i),a!=null&&l.push(go(n,a,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var $v=/\r\n?/g,Vv=/\u0000|\uFFFD/g;function yc(e){return(typeof e=="string"?e:""+e).replace($v,`
`).replace(Vv,"")}function Ko(e,t,n){if(t=yc(t),yc(e)!==t&&n)throw Error(L(425))}function Oi(){}var Fs=null,js=null;function Us(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Bs=typeof setTimeout=="function"?setTimeout:void 0,Hv=typeof clearTimeout=="function"?clearTimeout:void 0,wc=typeof Promise=="function"?Promise:void 0,Wv=typeof queueMicrotask=="function"?queueMicrotask:typeof wc<"u"?function(e){return wc.resolve(null).then(e).catch(Kv)}:Bs;function Kv(e){setTimeout(function(){throw e})}function Hl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),fo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);fo(t)}function nn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Sc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Cr=Math.random().toString(36).slice(2),St="__reactFiber$"+Cr,vo="__reactProps$"+Cr,zt="__reactContainer$"+Cr,$s="__reactEvents$"+Cr,Gv="__reactListeners$"+Cr,Qv="__reactHandles$"+Cr;function Sn(e){var t=e[St];if(t)return t;for(var n=e.parentNode;n;){if(t=n[zt]||n[St]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Sc(e);e!==null;){if(n=e[St])return n;e=Sc(e)}return t}e=n,n=e.parentNode}return null}function To(e){return e=e[St]||e[zt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Gn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(L(33))}function il(e){return e[vo]||null}var Vs=[],Qn=-1;function hn(e){return{current:e}}function oe(e){0>Qn||(e.current=Vs[Qn],Vs[Qn]=null,Qn--)}function te(e,t){Qn++,Vs[Qn]=e.current,e.current=t}var un={},Re=hn(un),Fe=hn(!1),kn=un;function pr(e,t){var n=e.type.contextTypes;if(!n)return un;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function je(e){return e=e.childContextTypes,e!=null}function Li(){oe(Fe),oe(Re)}function xc(e,t,n){if(Re.current!==un)throw Error(L(168));te(Re,t),te(Fe,n)}function Hf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(L(108,Og(e)||"Unknown",o));return ae({},n,r)}function Di(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||un,kn=Re.current,te(Re,e),te(Fe,Fe.current),!0}function _c(e,t,n){var r=e.stateNode;if(!r)throw Error(L(169));n?(e=Hf(e,t,kn),r.__reactInternalMemoizedMergedChildContext=e,oe(Fe),oe(Re),te(Re,e)):oe(Fe),te(Fe,n)}var Tt=null,ll=!1,Wl=!1;function Wf(e){Tt===null?Tt=[e]:Tt.push(e)}function Yv(e){ll=!0,Wf(e)}function mn(){if(!Wl&&Tt!==null){Wl=!0;var e=0,t=Z;try{var n=Tt;for(Z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Tt=null,ll=!1}catch(o){throw Tt!==null&&(Tt=Tt.slice(e+1)),gf(za,mn),o}finally{Z=t,Wl=!1}}return null}var Yn=[],Xn=0,Ii=null,zi=0,qe=[],et=0,bn=null,Ot=1,Lt="";function yn(e,t){Yn[Xn++]=zi,Yn[Xn++]=Ii,Ii=e,zi=t}function Kf(e,t,n){qe[et++]=Ot,qe[et++]=Lt,qe[et++]=bn,bn=e;var r=Ot;e=Lt;var o=32-ft(r)-1;r&=~(1<<o),n+=1;var i=32-ft(t)+o;if(30<i){var l=o-o%5;i=(r&(1<<l)-1).toString(32),r>>=l,o-=l,Ot=1<<32-ft(t)+o|n<<o|r,Lt=i+e}else Ot=1<<i|n<<o|r,Lt=e}function Wa(e){e.return!==null&&(yn(e,1),Kf(e,1,0))}function Ka(e){for(;e===Ii;)Ii=Yn[--Xn],Yn[Xn]=null,zi=Yn[--Xn],Yn[Xn]=null;for(;e===bn;)bn=qe[--et],qe[et]=null,Lt=qe[--et],qe[et]=null,Ot=qe[--et],qe[et]=null}var Ke=null,We=null,ie=!1,dt=null;function Gf(e,t){var n=nt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Cc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ke=e,We=nn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ke=e,We=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=bn!==null?{id:Ot,overflow:Lt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=nt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ke=e,We=null,!0):!1;default:return!1}}function Hs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ws(e){if(ie){var t=We;if(t){var n=t;if(!Cc(e,t)){if(Hs(e))throw Error(L(418));t=nn(n.nextSibling);var r=Ke;t&&Cc(e,t)?Gf(r,n):(e.flags=e.flags&-4097|2,ie=!1,Ke=e)}}else{if(Hs(e))throw Error(L(418));e.flags=e.flags&-4097|2,ie=!1,Ke=e}}}function Ec(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ke=e}function Go(e){if(e!==Ke)return!1;if(!ie)return Ec(e),ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Us(e.type,e.memoizedProps)),t&&(t=We)){if(Hs(e))throw Qf(),Error(L(418));for(;t;)Gf(e,t),t=nn(t.nextSibling)}if(Ec(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(L(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){We=nn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}We=null}}else We=Ke?nn(e.stateNode.nextSibling):null;return!0}function Qf(){for(var e=We;e;)e=nn(e.nextSibling)}function hr(){We=Ke=null,ie=!1}function Ga(e){dt===null?dt=[e]:dt.push(e)}var Xv=Bt.ReactCurrentBatchConfig;function Mr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(L(309));var r=n.stateNode}if(!r)throw Error(L(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=o.refs;l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(L(284));if(!n._owner)throw Error(L(290,e))}return e}function Qo(e,t){throw e=Object.prototype.toString.call(t),Error(L(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function kc(e){var t=e._init;return t(e._payload)}function Yf(e){function t(p,c){if(e){var v=p.deletions;v===null?(p.deletions=[c],p.flags|=16):v.push(c)}}function n(p,c){if(!e)return null;for(;c!==null;)t(p,c),c=c.sibling;return null}function r(p,c){for(p=new Map;c!==null;)c.key!==null?p.set(c.key,c):p.set(c.index,c),c=c.sibling;return p}function o(p,c){return p=sn(p,c),p.index=0,p.sibling=null,p}function i(p,c,v){return p.index=v,e?(v=p.alternate,v!==null?(v=v.index,v<c?(p.flags|=2,c):v):(p.flags|=2,c)):(p.flags|=1048576,c)}function l(p){return e&&p.alternate===null&&(p.flags|=2),p}function s(p,c,v,x){return c===null||c.tag!==6?(c=Jl(v,p.mode,x),c.return=p,c):(c=o(c,v),c.return=p,c)}function a(p,c,v,x){var C=v.type;return C===Vn?d(p,c,v.props.children,x,v.key):c!==null&&(c.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Kt&&kc(C)===c.type)?(x=o(c,v.props),x.ref=Mr(p,c,v),x.return=p,x):(x=vi(v.type,v.key,v.props,null,p.mode,x),x.ref=Mr(p,c,v),x.return=p,x)}function u(p,c,v,x){return c===null||c.tag!==4||c.stateNode.containerInfo!==v.containerInfo||c.stateNode.implementation!==v.implementation?(c=ql(v,p.mode,x),c.return=p,c):(c=o(c,v.children||[]),c.return=p,c)}function d(p,c,v,x,C){return c===null||c.tag!==7?(c=En(v,p.mode,x,C),c.return=p,c):(c=o(c,v),c.return=p,c)}function m(p,c,v){if(typeof c=="string"&&c!==""||typeof c=="number")return c=Jl(""+c,p.mode,v),c.return=p,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case Mo:return v=vi(c.type,c.key,c.props,null,p.mode,v),v.ref=Mr(p,null,c),v.return=p,v;case $n:return c=ql(c,p.mode,v),c.return=p,c;case Kt:var x=c._init;return m(p,x(c._payload),v)}if(Hr(c)||Or(c))return c=En(c,p.mode,v,null),c.return=p,c;Qo(p,c)}return null}function h(p,c,v,x){var C=c!==null?c.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return C!==null?null:s(p,c,""+v,x);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Mo:return v.key===C?a(p,c,v,x):null;case $n:return v.key===C?u(p,c,v,x):null;case Kt:return C=v._init,h(p,c,C(v._payload),x)}if(Hr(v)||Or(v))return C!==null?null:d(p,c,v,x,null);Qo(p,v)}return null}function y(p,c,v,x,C){if(typeof x=="string"&&x!==""||typeof x=="number")return p=p.get(v)||null,s(c,p,""+x,C);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Mo:return p=p.get(x.key===null?v:x.key)||null,a(c,p,x,C);case $n:return p=p.get(x.key===null?v:x.key)||null,u(c,p,x,C);case Kt:var R=x._init;return y(p,c,v,R(x._payload),C)}if(Hr(x)||Or(x))return p=p.get(v)||null,d(c,p,x,C,null);Qo(c,x)}return null}function w(p,c,v,x){for(var C=null,R=null,_=c,P=c=0,z=null;_!==null&&P<v.length;P++){_.index>P?(z=_,_=null):z=_.sibling;var E=h(p,_,v[P],x);if(E===null){_===null&&(_=z);break}e&&_&&E.alternate===null&&t(p,_),c=i(E,c,P),R===null?C=E:R.sibling=E,R=E,_=z}if(P===v.length)return n(p,_),ie&&yn(p,P),C;if(_===null){for(;P<v.length;P++)_=m(p,v[P],x),_!==null&&(c=i(_,c,P),R===null?C=_:R.sibling=_,R=_);return ie&&yn(p,P),C}for(_=r(p,_);P<v.length;P++)z=y(_,p,P,v[P],x),z!==null&&(e&&z.alternate!==null&&_.delete(z.key===null?P:z.key),c=i(z,c,P),R===null?C=z:R.sibling=z,R=z);return e&&_.forEach(function(O){return t(p,O)}),ie&&yn(p,P),C}function f(p,c,v,x){var C=Or(v);if(typeof C!="function")throw Error(L(150));if(v=C.call(v),v==null)throw Error(L(151));for(var R=C=null,_=c,P=c=0,z=null,E=v.next();_!==null&&!E.done;P++,E=v.next()){_.index>P?(z=_,_=null):z=_.sibling;var O=h(p,_,E.value,x);if(O===null){_===null&&(_=z);break}e&&_&&O.alternate===null&&t(p,_),c=i(O,c,P),R===null?C=O:R.sibling=O,R=O,_=z}if(E.done)return n(p,_),ie&&yn(p,P),C;if(_===null){for(;!E.done;P++,E=v.next())E=m(p,E.value,x),E!==null&&(c=i(E,c,P),R===null?C=E:R.sibling=E,R=E);return ie&&yn(p,P),C}for(_=r(p,_);!E.done;P++,E=v.next())E=y(_,p,P,E.value,x),E!==null&&(e&&E.alternate!==null&&_.delete(E.key===null?P:E.key),c=i(E,c,P),R===null?C=E:R.sibling=E,R=E);return e&&_.forEach(function(N){return t(p,N)}),ie&&yn(p,P),C}function S(p,c,v,x){if(typeof v=="object"&&v!==null&&v.type===Vn&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Mo:e:{for(var C=v.key,R=c;R!==null;){if(R.key===C){if(C=v.type,C===Vn){if(R.tag===7){n(p,R.sibling),c=o(R,v.props.children),c.return=p,p=c;break e}}else if(R.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Kt&&kc(C)===R.type){n(p,R.sibling),c=o(R,v.props),c.ref=Mr(p,R,v),c.return=p,p=c;break e}n(p,R);break}else t(p,R);R=R.sibling}v.type===Vn?(c=En(v.props.children,p.mode,x,v.key),c.return=p,p=c):(x=vi(v.type,v.key,v.props,null,p.mode,x),x.ref=Mr(p,c,v),x.return=p,p=x)}return l(p);case $n:e:{for(R=v.key;c!==null;){if(c.key===R)if(c.tag===4&&c.stateNode.containerInfo===v.containerInfo&&c.stateNode.implementation===v.implementation){n(p,c.sibling),c=o(c,v.children||[]),c.return=p,p=c;break e}else{n(p,c);break}else t(p,c);c=c.sibling}c=ql(v,p.mode,x),c.return=p,p=c}return l(p);case Kt:return R=v._init,S(p,c,R(v._payload),x)}if(Hr(v))return w(p,c,v,x);if(Or(v))return f(p,c,v,x);Qo(p,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,c!==null&&c.tag===6?(n(p,c.sibling),c=o(c,v),c.return=p,p=c):(n(p,c),c=Jl(v,p.mode,x),c.return=p,p=c),l(p)):n(p,c)}return S}var mr=Yf(!0),Xf=Yf(!1),Mi=hn(null),Fi=null,Zn=null,Qa=null;function Ya(){Qa=Zn=Fi=null}function Xa(e){var t=Mi.current;oe(Mi),e._currentValue=t}function Ks(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function lr(e,t){Fi=e,Qa=Zn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Me=!0),e.firstContext=null)}function ot(e){var t=e._currentValue;if(Qa!==e)if(e={context:e,memoizedValue:t,next:null},Zn===null){if(Fi===null)throw Error(L(308));Zn=e,Fi.dependencies={lanes:0,firstContext:e}}else Zn=Zn.next=e;return t}var xn=null;function Za(e){xn===null?xn=[e]:xn.push(e)}function Zf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Za(t)):(n.next=o.next,o.next=n),t.interleaved=n,Mt(e,r)}function Mt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Gt=!1;function Ja(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Jf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function rn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Y&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Mt(e,n)}return o=r.interleaved,o===null?(t.next=t,Za(r)):(t.next=o.next,o.next=t),r.interleaved=t,Mt(e,n)}function di(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ma(e,n)}}function bc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ji(e,t,n,r){var o=e.updateQueue;Gt=!1;var i=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var d=e.alternate;d!==null&&(d=d.updateQueue,s=d.lastBaseUpdate,s!==l&&(s===null?d.firstBaseUpdate=u:s.next=u,d.lastBaseUpdate=a))}if(i!==null){var m=o.baseState;l=0,d=u=a=null,s=i;do{var h=s.lane,y=s.eventTime;if((r&h)===h){d!==null&&(d=d.next={eventTime:y,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var w=e,f=s;switch(h=t,y=n,f.tag){case 1:if(w=f.payload,typeof w=="function"){m=w.call(y,m,h);break e}m=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=f.payload,h=typeof w=="function"?w.call(y,m,h):w,h==null)break e;m=ae({},m,h);break e;case 2:Gt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,h=o.effects,h===null?o.effects=[s]:h.push(s))}else y={eventTime:y,lane:h,tag:s.tag,payload:s.payload,callback:s.callback,next:null},d===null?(u=d=y,a=m):d=d.next=y,l|=h;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;h=s,s=h.next,h.next=null,o.lastBaseUpdate=h,o.shared.pending=null}}while(!0);if(d===null&&(a=m),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Rn|=l,e.lanes=l,e.memoizedState=m}}function Pc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(L(191,o));o.call(r)}}}var Oo={},Ct=hn(Oo),yo=hn(Oo),wo=hn(Oo);function _n(e){if(e===Oo)throw Error(L(174));return e}function qa(e,t){switch(te(wo,t),te(yo,e),te(Ct,Oo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:bs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=bs(t,e)}oe(Ct),te(Ct,t)}function gr(){oe(Ct),oe(yo),oe(wo)}function qf(e){_n(wo.current);var t=_n(Ct.current),n=bs(t,e.type);t!==n&&(te(yo,e),te(Ct,n))}function eu(e){yo.current===e&&(oe(Ct),oe(yo))}var le=hn(0);function Ui(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Kl=[];function tu(){for(var e=0;e<Kl.length;e++)Kl[e]._workInProgressVersionPrimary=null;Kl.length=0}var fi=Bt.ReactCurrentDispatcher,Gl=Bt.ReactCurrentBatchConfig,Pn=0,se=null,me=null,ye=null,Bi=!1,qr=!1,So=0,Zv=0;function ke(){throw Error(L(321))}function nu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ht(e[n],t[n]))return!1;return!0}function ru(e,t,n,r,o,i){if(Pn=i,se=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,fi.current=e===null||e.memoizedState===null?ty:ny,e=n(r,o),qr){i=0;do{if(qr=!1,So=0,25<=i)throw Error(L(301));i+=1,ye=me=null,t.updateQueue=null,fi.current=ry,e=n(r,o)}while(qr)}if(fi.current=$i,t=me!==null&&me.next!==null,Pn=0,ye=me=se=null,Bi=!1,t)throw Error(L(300));return e}function ou(){var e=So!==0;return So=0,e}function wt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ye===null?se.memoizedState=ye=e:ye=ye.next=e,ye}function it(){if(me===null){var e=se.alternate;e=e!==null?e.memoizedState:null}else e=me.next;var t=ye===null?se.memoizedState:ye.next;if(t!==null)ye=t,me=e;else{if(e===null)throw Error(L(310));me=e,e={memoizedState:me.memoizedState,baseState:me.baseState,baseQueue:me.baseQueue,queue:me.queue,next:null},ye===null?se.memoizedState=ye=e:ye=ye.next=e}return ye}function xo(e,t){return typeof t=="function"?t(e):t}function Ql(e){var t=it(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=me,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=l=null,a=null,u=i;do{var d=u.lane;if((Pn&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var m={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=m,l=r):a=a.next=m,se.lanes|=d,Rn|=d}u=u.next}while(u!==null&&u!==i);a===null?l=r:a.next=s,ht(r,t.memoizedState)||(Me=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,se.lanes|=i,Rn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Yl(e){var t=it(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do i=e(i,l.action),l=l.next;while(l!==o);ht(i,t.memoizedState)||(Me=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ep(){}function tp(e,t){var n=se,r=it(),o=t(),i=!ht(r.memoizedState,o);if(i&&(r.memoizedState=o,Me=!0),r=r.queue,iu(op.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ye!==null&&ye.memoizedState.tag&1){if(n.flags|=2048,_o(9,rp.bind(null,n,r,o,t),void 0,null),we===null)throw Error(L(349));Pn&30||np(n,t,o)}return o}function np(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=se.updateQueue,t===null?(t={lastEffect:null,stores:null},se.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function rp(e,t,n,r){t.value=n,t.getSnapshot=r,ip(t)&&lp(e)}function op(e,t,n){return n(function(){ip(t)&&lp(e)})}function ip(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ht(e,n)}catch{return!0}}function lp(e){var t=Mt(e,1);t!==null&&pt(t,e,1,-1)}function Rc(e){var t=wt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=ey.bind(null,se,e),[t.memoizedState,e]}function _o(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=se.updateQueue,t===null?(t={lastEffect:null,stores:null},se.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function sp(){return it().memoizedState}function pi(e,t,n,r){var o=wt();se.flags|=e,o.memoizedState=_o(1|t,n,void 0,r===void 0?null:r)}function sl(e,t,n,r){var o=it();r=r===void 0?null:r;var i=void 0;if(me!==null){var l=me.memoizedState;if(i=l.destroy,r!==null&&nu(r,l.deps)){o.memoizedState=_o(t,n,i,r);return}}se.flags|=e,o.memoizedState=_o(1|t,n,i,r)}function Nc(e,t){return pi(8390656,8,e,t)}function iu(e,t){return sl(2048,8,e,t)}function ap(e,t){return sl(4,2,e,t)}function up(e,t){return sl(4,4,e,t)}function cp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function dp(e,t,n){return n=n!=null?n.concat([e]):null,sl(4,4,cp.bind(null,t,e),n)}function lu(){}function fp(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&nu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function pp(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&nu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function hp(e,t,n){return Pn&21?(ht(n,t)||(n=wf(),se.lanes|=n,Rn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Me=!0),e.memoizedState=n)}function Jv(e,t){var n=Z;Z=n!==0&&4>n?n:4,e(!0);var r=Gl.transition;Gl.transition={};try{e(!1),t()}finally{Z=n,Gl.transition=r}}function mp(){return it().memoizedState}function qv(e,t,n){var r=ln(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},gp(e))vp(t,n);else if(n=Zf(e,t,n,r),n!==null){var o=Te();pt(n,e,r,o),yp(n,t,r)}}function ey(e,t,n){var r=ln(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(gp(e))vp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,n);if(o.hasEagerState=!0,o.eagerState=s,ht(s,l)){var a=t.interleaved;a===null?(o.next=o,Za(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Zf(e,t,o,r),n!==null&&(o=Te(),pt(n,e,r,o),yp(n,t,r))}}function gp(e){var t=e.alternate;return e===se||t!==null&&t===se}function vp(e,t){qr=Bi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function yp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ma(e,n)}}var $i={readContext:ot,useCallback:ke,useContext:ke,useEffect:ke,useImperativeHandle:ke,useInsertionEffect:ke,useLayoutEffect:ke,useMemo:ke,useReducer:ke,useRef:ke,useState:ke,useDebugValue:ke,useDeferredValue:ke,useTransition:ke,useMutableSource:ke,useSyncExternalStore:ke,useId:ke,unstable_isNewReconciler:!1},ty={readContext:ot,useCallback:function(e,t){return wt().memoizedState=[e,t===void 0?null:t],e},useContext:ot,useEffect:Nc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,pi(4194308,4,cp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return pi(4194308,4,e,t)},useInsertionEffect:function(e,t){return pi(4,2,e,t)},useMemo:function(e,t){var n=wt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=wt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qv.bind(null,se,e),[r.memoizedState,e]},useRef:function(e){var t=wt();return e={current:e},t.memoizedState=e},useState:Rc,useDebugValue:lu,useDeferredValue:function(e){return wt().memoizedState=e},useTransition:function(){var e=Rc(!1),t=e[0];return e=Jv.bind(null,e[1]),wt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=se,o=wt();if(ie){if(n===void 0)throw Error(L(407));n=n()}else{if(n=t(),we===null)throw Error(L(349));Pn&30||np(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Nc(op.bind(null,r,i,e),[e]),r.flags|=2048,_o(9,rp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=wt(),t=we.identifierPrefix;if(ie){var n=Lt,r=Ot;n=(r&~(1<<32-ft(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=So++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Zv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ny={readContext:ot,useCallback:fp,useContext:ot,useEffect:iu,useImperativeHandle:dp,useInsertionEffect:ap,useLayoutEffect:up,useMemo:pp,useReducer:Ql,useRef:sp,useState:function(){return Ql(xo)},useDebugValue:lu,useDeferredValue:function(e){var t=it();return hp(t,me.memoizedState,e)},useTransition:function(){var e=Ql(xo)[0],t=it().memoizedState;return[e,t]},useMutableSource:ep,useSyncExternalStore:tp,useId:mp,unstable_isNewReconciler:!1},ry={readContext:ot,useCallback:fp,useContext:ot,useEffect:iu,useImperativeHandle:dp,useInsertionEffect:ap,useLayoutEffect:up,useMemo:pp,useReducer:Yl,useRef:sp,useState:function(){return Yl(xo)},useDebugValue:lu,useDeferredValue:function(e){var t=it();return me===null?t.memoizedState=e:hp(t,me.memoizedState,e)},useTransition:function(){var e=Yl(xo)[0],t=it().memoizedState;return[e,t]},useMutableSource:ep,useSyncExternalStore:tp,useId:mp,unstable_isNewReconciler:!1};function ut(e,t){if(e&&e.defaultProps){t=ae({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Gs(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ae({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var al={isMounted:function(e){return(e=e._reactInternals)?In(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Te(),o=ln(e),i=Dt(r,o);i.payload=t,n!=null&&(i.callback=n),t=rn(e,i,o),t!==null&&(pt(t,e,o,r),di(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Te(),o=ln(e),i=Dt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=rn(e,i,o),t!==null&&(pt(t,e,o,r),di(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Te(),r=ln(e),o=Dt(n,r);o.tag=2,t!=null&&(o.callback=t),t=rn(e,o,r),t!==null&&(pt(t,e,r,n),di(t,e,r))}};function Ac(e,t,n,r,o,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!ho(n,r)||!ho(o,i):!0}function wp(e,t,n){var r=!1,o=un,i=t.contextType;return typeof i=="object"&&i!==null?i=ot(i):(o=je(t)?kn:Re.current,r=t.contextTypes,i=(r=r!=null)?pr(e,o):un),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=al,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Tc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&al.enqueueReplaceState(t,t.state,null)}function Qs(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Ja(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=ot(i):(i=je(t)?kn:Re.current,o.context=pr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Gs(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&al.enqueueReplaceState(o,o.state,null),ji(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function vr(e,t){try{var n="",r=t;do n+=Tg(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Xl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ys(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var oy=typeof WeakMap=="function"?WeakMap:Map;function Sp(e,t,n){n=Dt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hi||(Hi=!0,ia=r),Ys(e,t)},n}function xp(e,t,n){n=Dt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ys(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Ys(e,t),typeof r!="function"&&(on===null?on=new Set([this]):on.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function Oc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new oy;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=yy.bind(null,e,t,n),t.then(e,e))}function Lc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Dc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Dt(-1,1),t.tag=2,rn(n,t,1))),n.lanes|=1),e)}var iy=Bt.ReactCurrentOwner,Me=!1;function Ae(e,t,n,r){t.child=e===null?Xf(t,null,n,r):mr(t,e.child,n,r)}function Ic(e,t,n,r,o){n=n.render;var i=t.ref;return lr(t,o),r=ru(e,t,n,r,i,o),n=ou(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ft(e,t,o)):(ie&&n&&Wa(t),t.flags|=1,Ae(e,t,r,o),t.child)}function zc(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!hu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,_p(e,t,i,r,o)):(e=vi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:ho,n(l,r)&&e.ref===t.ref)return Ft(e,t,o)}return t.flags|=1,e=sn(i,r),e.ref=t.ref,e.return=t,t.child=e}function _p(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(ho(i,r)&&e.ref===t.ref)if(Me=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Me=!0);else return t.lanes=e.lanes,Ft(e,t,o)}return Xs(e,t,n,r,o)}function Cp(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},te(qn,Ve),Ve|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,te(qn,Ve),Ve|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,te(qn,Ve),Ve|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,te(qn,Ve),Ve|=r;return Ae(e,t,o,n),t.child}function Ep(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Xs(e,t,n,r,o){var i=je(n)?kn:Re.current;return i=pr(t,i),lr(t,o),n=ru(e,t,n,r,i,o),r=ou(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ft(e,t,o)):(ie&&r&&Wa(t),t.flags|=1,Ae(e,t,n,o),t.child)}function Mc(e,t,n,r,o){if(je(n)){var i=!0;Di(t)}else i=!1;if(lr(t,o),t.stateNode===null)hi(e,t),wp(t,n,r),Qs(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=ot(u):(u=je(n)?kn:Re.current,u=pr(t,u));var d=n.getDerivedStateFromProps,m=typeof d=="function"||typeof l.getSnapshotBeforeUpdate=="function";m||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||a!==u)&&Tc(t,l,r,u),Gt=!1;var h=t.memoizedState;l.state=h,ji(t,r,l,o),a=t.memoizedState,s!==r||h!==a||Fe.current||Gt?(typeof d=="function"&&(Gs(t,n,d,r),a=t.memoizedState),(s=Gt||Ac(t,n,s,r,h,a,u))?(m||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Jf(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:ut(t.type,s),l.props=u,m=t.pendingProps,h=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=ot(a):(a=je(n)?kn:Re.current,a=pr(t,a));var y=n.getDerivedStateFromProps;(d=typeof y=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==m||h!==a)&&Tc(t,l,r,a),Gt=!1,h=t.memoizedState,l.state=h,ji(t,r,l,o);var w=t.memoizedState;s!==m||h!==w||Fe.current||Gt?(typeof y=="function"&&(Gs(t,n,y,r),w=t.memoizedState),(u=Gt||Ac(t,n,u,r,h,w,a)||!1)?(d||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,w,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,w,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),l.props=r,l.state=w,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Zs(e,t,n,r,i,o)}function Zs(e,t,n,r,o,i){Ep(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&_c(t,n,!1),Ft(e,t,i);r=t.stateNode,iy.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=mr(t,e.child,null,i),t.child=mr(t,null,s,i)):Ae(e,t,s,i),t.memoizedState=r.state,o&&_c(t,n,!0),t.child}function kp(e){var t=e.stateNode;t.pendingContext?xc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&xc(e,t.context,!1),qa(e,t.containerInfo)}function Fc(e,t,n,r,o){return hr(),Ga(o),t.flags|=256,Ae(e,t,n,r),t.child}var Js={dehydrated:null,treeContext:null,retryLane:0};function qs(e){return{baseLanes:e,cachePool:null,transitions:null}}function bp(e,t,n){var r=t.pendingProps,o=le.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),te(le,o&1),e===null)return Ws(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=dl(l,r,0,null),e=En(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=qs(n),t.memoizedState=Js,e):su(t,l));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return ly(e,t,l,r,s,o,n);if(i){i=r.fallback,l=t.mode,o=e.child,s=o.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=sn(o,a),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=sn(s,i):(i=En(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?qs(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=Js,r}return i=e.child,e=i.sibling,r=sn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function su(e,t){return t=dl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Yo(e,t,n,r){return r!==null&&Ga(r),mr(t,e.child,null,n),e=su(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ly(e,t,n,r,o,i,l){if(n)return t.flags&256?(t.flags&=-257,r=Xl(Error(L(422))),Yo(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=dl({mode:"visible",children:r.children},o,0,null),i=En(i,o,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&mr(t,e.child,null,l),t.child.memoizedState=qs(l),t.memoizedState=Js,i);if(!(t.mode&1))return Yo(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(L(419)),r=Xl(i,r,void 0),Yo(e,t,l,r)}if(s=(l&e.childLanes)!==0,Me||s){if(r=we,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Mt(e,o),pt(r,e,o,-1))}return pu(),r=Xl(Error(L(421))),Yo(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=wy.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,We=nn(o.nextSibling),Ke=t,ie=!0,dt=null,e!==null&&(qe[et++]=Ot,qe[et++]=Lt,qe[et++]=bn,Ot=e.id,Lt=e.overflow,bn=t),t=su(t,r.children),t.flags|=4096,t)}function jc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ks(e.return,t,n)}function Zl(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Pp(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Ae(e,t,r.children,n),r=le.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&jc(e,n,t);else if(e.tag===19)jc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(te(le,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ui(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Zl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ui(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Zl(t,!0,n,null,i);break;case"together":Zl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function hi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ft(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Rn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(L(153));if(t.child!==null){for(e=t.child,n=sn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=sn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function sy(e,t,n){switch(t.tag){case 3:kp(t),hr();break;case 5:qf(t);break;case 1:je(t.type)&&Di(t);break;case 4:qa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;te(Mi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(te(le,le.current&1),t.flags|=128,null):n&t.child.childLanes?bp(e,t,n):(te(le,le.current&1),e=Ft(e,t,n),e!==null?e.sibling:null);te(le,le.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Pp(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),te(le,le.current),r)break;return null;case 22:case 23:return t.lanes=0,Cp(e,t,n)}return Ft(e,t,n)}var Rp,ea,Np,Ap;Rp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ea=function(){};Np=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,_n(Ct.current);var i=null;switch(n){case"input":o=_s(e,o),r=_s(e,r),i=[];break;case"select":o=ae({},o,{value:void 0}),r=ae({},r,{value:void 0}),i=[];break;case"textarea":o=ks(e,o),r=ks(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Oi)}Ps(n,r);var l;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(lo.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(lo.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&re("scroll",e),i||s===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Ap=function(e,t,n,r){n!==r&&(t.flags|=4)};function Fr(e,t){if(!ie)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function be(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ay(e,t,n){var r=t.pendingProps;switch(Ka(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return be(t),null;case 1:return je(t.type)&&Li(),be(t),null;case 3:return r=t.stateNode,gr(),oe(Fe),oe(Re),tu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Go(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,dt!==null&&(aa(dt),dt=null))),ea(e,t),be(t),null;case 5:eu(t);var o=_n(wo.current);if(n=t.type,e!==null&&t.stateNode!=null)Np(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(L(166));return be(t),null}if(e=_n(Ct.current),Go(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[St]=t,r[vo]=i,e=(t.mode&1)!==0,n){case"dialog":re("cancel",r),re("close",r);break;case"iframe":case"object":case"embed":re("load",r);break;case"video":case"audio":for(o=0;o<Kr.length;o++)re(Kr[o],r);break;case"source":re("error",r);break;case"img":case"image":case"link":re("error",r),re("load",r);break;case"details":re("toggle",r);break;case"input":Qu(r,i),re("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},re("invalid",r);break;case"textarea":Xu(r,i),re("invalid",r)}Ps(n,i),o=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&Ko(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&Ko(r.textContent,s,e),o=["children",""+s]):lo.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&re("scroll",r)}switch(n){case"input":Fo(r),Yu(r,i,!0);break;case"textarea":Fo(r),Zu(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Oi)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=rf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[St]=t,e[vo]=r,Rp(e,t,!1,!1),t.stateNode=e;e:{switch(l=Rs(n,r),n){case"dialog":re("cancel",e),re("close",e),o=r;break;case"iframe":case"object":case"embed":re("load",e),o=r;break;case"video":case"audio":for(o=0;o<Kr.length;o++)re(Kr[o],e);o=r;break;case"source":re("error",e),o=r;break;case"img":case"image":case"link":re("error",e),re("load",e),o=r;break;case"details":re("toggle",e),o=r;break;case"input":Qu(e,r),o=_s(e,r),re("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ae({},r,{value:void 0}),re("invalid",e);break;case"textarea":Xu(e,r),o=ks(e,r),re("invalid",e);break;default:o=r}Ps(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="style"?sf(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&of(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&so(e,a):typeof a=="number"&&so(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(lo.hasOwnProperty(i)?a!=null&&i==="onScroll"&&re("scroll",e):a!=null&&Ta(e,i,a,l))}switch(n){case"input":Fo(e),Yu(e,r,!1);break;case"textarea":Fo(e),Zu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+an(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?nr(e,!!r.multiple,i,!1):r.defaultValue!=null&&nr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Oi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return be(t),null;case 6:if(e&&t.stateNode!=null)Ap(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(L(166));if(n=_n(wo.current),_n(Ct.current),Go(t)){if(r=t.stateNode,n=t.memoizedProps,r[St]=t,(i=r.nodeValue!==n)&&(e=Ke,e!==null))switch(e.tag){case 3:Ko(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ko(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[St]=t,t.stateNode=r}return be(t),null;case 13:if(oe(le),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ie&&We!==null&&t.mode&1&&!(t.flags&128))Qf(),hr(),t.flags|=98560,i=!1;else if(i=Go(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(L(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(L(317));i[St]=t}else hr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;be(t),i=!1}else dt!==null&&(aa(dt),dt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||le.current&1?ge===0&&(ge=3):pu())),t.updateQueue!==null&&(t.flags|=4),be(t),null);case 4:return gr(),ea(e,t),e===null&&mo(t.stateNode.containerInfo),be(t),null;case 10:return Xa(t.type._context),be(t),null;case 17:return je(t.type)&&Li(),be(t),null;case 19:if(oe(le),i=t.memoizedState,i===null)return be(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)Fr(i,!1);else{if(ge!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Ui(e),l!==null){for(t.flags|=128,Fr(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return te(le,le.current&1|2),t.child}e=e.sibling}i.tail!==null&&de()>yr&&(t.flags|=128,r=!0,Fr(i,!1),t.lanes=4194304)}else{if(!r)if(e=Ui(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Fr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!ie)return be(t),null}else 2*de()-i.renderingStartTime>yr&&n!==1073741824&&(t.flags|=128,r=!0,Fr(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=de(),t.sibling=null,n=le.current,te(le,r?n&1|2:n&1),t):(be(t),null);case 22:case 23:return fu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ve&1073741824&&(be(t),t.subtreeFlags&6&&(t.flags|=8192)):be(t),null;case 24:return null;case 25:return null}throw Error(L(156,t.tag))}function uy(e,t){switch(Ka(t),t.tag){case 1:return je(t.type)&&Li(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return gr(),oe(Fe),oe(Re),tu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return eu(t),null;case 13:if(oe(le),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(L(340));hr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(le),null;case 4:return gr(),null;case 10:return Xa(t.type._context),null;case 22:case 23:return fu(),null;case 24:return null;default:return null}}var Xo=!1,Pe=!1,cy=typeof WeakSet=="function"?WeakSet:Set,M=null;function Jn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ue(e,t,r)}else n.current=null}function ta(e,t,n){try{n()}catch(r){ue(e,t,r)}}var Uc=!1;function dy(e,t){if(Fs=Ni,e=If(),Ha(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,s=-1,a=-1,u=0,d=0,m=e,h=null;t:for(;;){for(var y;m!==n||o!==0&&m.nodeType!==3||(s=l+o),m!==i||r!==0&&m.nodeType!==3||(a=l+r),m.nodeType===3&&(l+=m.nodeValue.length),(y=m.firstChild)!==null;)h=m,m=y;for(;;){if(m===e)break t;if(h===n&&++u===o&&(s=l),h===i&&++d===r&&(a=l),(y=m.nextSibling)!==null)break;m=h,h=m.parentNode}m=y}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(js={focusedElem:e,selectionRange:n},Ni=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var f=w.memoizedProps,S=w.memoizedState,p=t.stateNode,c=p.getSnapshotBeforeUpdate(t.elementType===t.type?f:ut(t.type,f),S);p.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(L(163))}}catch(x){ue(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return w=Uc,Uc=!1,w}function eo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&ta(t,n,i)}o=o.next}while(o!==r)}}function ul(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function na(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Tp(e){var t=e.alternate;t!==null&&(e.alternate=null,Tp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[St],delete t[vo],delete t[$s],delete t[Gv],delete t[Qv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Op(e){return e.tag===5||e.tag===3||e.tag===4}function Bc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Op(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ra(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Oi));else if(r!==4&&(e=e.child,e!==null))for(ra(e,t,n),e=e.sibling;e!==null;)ra(e,t,n),e=e.sibling}function oa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(oa(e,t,n),e=e.sibling;e!==null;)oa(e,t,n),e=e.sibling}var Se=null,ct=!1;function $t(e,t,n){for(n=n.child;n!==null;)Lp(e,t,n),n=n.sibling}function Lp(e,t,n){if(_t&&typeof _t.onCommitFiberUnmount=="function")try{_t.onCommitFiberUnmount(tl,n)}catch{}switch(n.tag){case 5:Pe||Jn(n,t);case 6:var r=Se,o=ct;Se=null,$t(e,t,n),Se=r,ct=o,Se!==null&&(ct?(e=Se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Se.removeChild(n.stateNode));break;case 18:Se!==null&&(ct?(e=Se,n=n.stateNode,e.nodeType===8?Hl(e.parentNode,n):e.nodeType===1&&Hl(e,n),fo(e)):Hl(Se,n.stateNode));break;case 4:r=Se,o=ct,Se=n.stateNode.containerInfo,ct=!0,$t(e,t,n),Se=r,ct=o;break;case 0:case 11:case 14:case 15:if(!Pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&ta(n,t,l),o=o.next}while(o!==r)}$t(e,t,n);break;case 1:if(!Pe&&(Jn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){ue(n,t,s)}$t(e,t,n);break;case 21:$t(e,t,n);break;case 22:n.mode&1?(Pe=(r=Pe)||n.memoizedState!==null,$t(e,t,n),Pe=r):$t(e,t,n);break;default:$t(e,t,n)}}function $c(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new cy),t.forEach(function(r){var o=Sy.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function lt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:Se=s.stateNode,ct=!1;break e;case 3:Se=s.stateNode.containerInfo,ct=!0;break e;case 4:Se=s.stateNode.containerInfo,ct=!0;break e}s=s.return}if(Se===null)throw Error(L(160));Lp(i,l,o),Se=null,ct=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){ue(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Dp(t,e),t=t.sibling}function Dp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(lt(t,e),vt(e),r&4){try{eo(3,e,e.return),ul(3,e)}catch(f){ue(e,e.return,f)}try{eo(5,e,e.return)}catch(f){ue(e,e.return,f)}}break;case 1:lt(t,e),vt(e),r&512&&n!==null&&Jn(n,n.return);break;case 5:if(lt(t,e),vt(e),r&512&&n!==null&&Jn(n,n.return),e.flags&32){var o=e.stateNode;try{so(o,"")}catch(f){ue(e,e.return,f)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&tf(o,i),Rs(s,l);var u=Rs(s,i);for(l=0;l<a.length;l+=2){var d=a[l],m=a[l+1];d==="style"?sf(o,m):d==="dangerouslySetInnerHTML"?of(o,m):d==="children"?so(o,m):Ta(o,d,m,u)}switch(s){case"input":Cs(o,i);break;case"textarea":nf(o,i);break;case"select":var h=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?nr(o,!!i.multiple,y,!1):h!==!!i.multiple&&(i.defaultValue!=null?nr(o,!!i.multiple,i.defaultValue,!0):nr(o,!!i.multiple,i.multiple?[]:"",!1))}o[vo]=i}catch(f){ue(e,e.return,f)}}break;case 6:if(lt(t,e),vt(e),r&4){if(e.stateNode===null)throw Error(L(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(f){ue(e,e.return,f)}}break;case 3:if(lt(t,e),vt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{fo(t.containerInfo)}catch(f){ue(e,e.return,f)}break;case 4:lt(t,e),vt(e);break;case 13:lt(t,e),vt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(cu=de())),r&4&&$c(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(Pe=(u=Pe)||d,lt(t,e),Pe=u):lt(t,e),vt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(M=e,d=e.child;d!==null;){for(m=M=d;M!==null;){switch(h=M,y=h.child,h.tag){case 0:case 11:case 14:case 15:eo(4,h,h.return);break;case 1:Jn(h,h.return);var w=h.stateNode;if(typeof w.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(f){ue(r,n,f)}}break;case 5:Jn(h,h.return);break;case 22:if(h.memoizedState!==null){Hc(m);continue}}y!==null?(y.return=h,M=y):Hc(m)}d=d.sibling}e:for(d=null,m=e;;){if(m.tag===5){if(d===null){d=m;try{o=m.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=m.stateNode,a=m.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=lf("display",l))}catch(f){ue(e,e.return,f)}}}else if(m.tag===6){if(d===null)try{m.stateNode.nodeValue=u?"":m.memoizedProps}catch(f){ue(e,e.return,f)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;d===m&&(d=null),m=m.return}d===m&&(d=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:lt(t,e),vt(e),r&4&&$c(e);break;case 21:break;default:lt(t,e),vt(e)}}function vt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Op(n)){var r=n;break e}n=n.return}throw Error(L(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(so(o,""),r.flags&=-33);var i=Bc(e);oa(e,i,o);break;case 3:case 4:var l=r.stateNode.containerInfo,s=Bc(e);ra(e,s,l);break;default:throw Error(L(161))}}catch(a){ue(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function fy(e,t,n){M=e,Ip(e)}function Ip(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var o=M,i=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||Xo;if(!l){var s=o.alternate,a=s!==null&&s.memoizedState!==null||Pe;s=Xo;var u=Pe;if(Xo=l,(Pe=a)&&!u)for(M=o;M!==null;)l=M,a=l.child,l.tag===22&&l.memoizedState!==null?Wc(o):a!==null?(a.return=l,M=a):Wc(o);for(;i!==null;)M=i,Ip(i),i=i.sibling;M=o,Xo=s,Pe=u}Vc(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,M=i):Vc(e)}}function Vc(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Pe||ul(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Pe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ut(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Pc(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Pc(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var m=d.dehydrated;m!==null&&fo(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(L(163))}Pe||t.flags&512&&na(t)}catch(h){ue(t,t.return,h)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Hc(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Wc(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ul(4,t)}catch(a){ue(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){ue(t,o,a)}}var i=t.return;try{na(t)}catch(a){ue(t,i,a)}break;case 5:var l=t.return;try{na(t)}catch(a){ue(t,l,a)}}}catch(a){ue(t,t.return,a)}if(t===e){M=null;break}var s=t.sibling;if(s!==null){s.return=t.return,M=s;break}M=t.return}}var py=Math.ceil,Vi=Bt.ReactCurrentDispatcher,au=Bt.ReactCurrentOwner,rt=Bt.ReactCurrentBatchConfig,Y=0,we=null,fe=null,_e=0,Ve=0,qn=hn(0),ge=0,Co=null,Rn=0,cl=0,uu=0,to=null,ze=null,cu=0,yr=1/0,At=null,Hi=!1,ia=null,on=null,Zo=!1,Jt=null,Wi=0,no=0,la=null,mi=-1,gi=0;function Te(){return Y&6?de():mi!==-1?mi:mi=de()}function ln(e){return e.mode&1?Y&2&&_e!==0?_e&-_e:Xv.transition!==null?(gi===0&&(gi=wf()),gi):(e=Z,e!==0||(e=window.event,e=e===void 0?16:bf(e.type)),e):1}function pt(e,t,n,r){if(50<no)throw no=0,la=null,Error(L(185));No(e,n,r),(!(Y&2)||e!==we)&&(e===we&&(!(Y&2)&&(cl|=n),ge===4&&Xt(e,_e)),Ue(e,r),n===1&&Y===0&&!(t.mode&1)&&(yr=de()+500,ll&&mn()))}function Ue(e,t){var n=e.callbackNode;Xg(e,t);var r=Ri(e,e===we?_e:0);if(r===0)n!==null&&ec(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ec(n),t===1)e.tag===0?Yv(Kc.bind(null,e)):Wf(Kc.bind(null,e)),Wv(function(){!(Y&6)&&mn()}),n=null;else{switch(Sf(r)){case 1:n=za;break;case 4:n=vf;break;case 16:n=Pi;break;case 536870912:n=yf;break;default:n=Pi}n=Vp(n,zp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function zp(e,t){if(mi=-1,gi=0,Y&6)throw Error(L(327));var n=e.callbackNode;if(sr()&&e.callbackNode!==n)return null;var r=Ri(e,e===we?_e:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ki(e,r);else{t=r;var o=Y;Y|=2;var i=Fp();(we!==e||_e!==t)&&(At=null,yr=de()+500,Cn(e,t));do try{gy();break}catch(s){Mp(e,s)}while(!0);Ya(),Vi.current=i,Y=o,fe!==null?t=0:(we=null,_e=0,t=ge)}if(t!==0){if(t===2&&(o=Ls(e),o!==0&&(r=o,t=sa(e,o))),t===1)throw n=Co,Cn(e,0),Xt(e,r),Ue(e,de()),n;if(t===6)Xt(e,r);else{if(o=e.current.alternate,!(r&30)&&!hy(o)&&(t=Ki(e,r),t===2&&(i=Ls(e),i!==0&&(r=i,t=sa(e,i))),t===1))throw n=Co,Cn(e,0),Xt(e,r),Ue(e,de()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(L(345));case 2:wn(e,ze,At);break;case 3:if(Xt(e,r),(r&130023424)===r&&(t=cu+500-de(),10<t)){if(Ri(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Te(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Bs(wn.bind(null,e,ze,At),t);break}wn(e,ze,At);break;case 4:if(Xt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-ft(r);i=1<<l,l=t[l],l>o&&(o=l),r&=~i}if(r=o,r=de()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*py(r/1960))-r,10<r){e.timeoutHandle=Bs(wn.bind(null,e,ze,At),r);break}wn(e,ze,At);break;case 5:wn(e,ze,At);break;default:throw Error(L(329))}}}return Ue(e,de()),e.callbackNode===n?zp.bind(null,e):null}function sa(e,t){var n=to;return e.current.memoizedState.isDehydrated&&(Cn(e,t).flags|=256),e=Ki(e,t),e!==2&&(t=ze,ze=n,t!==null&&aa(t)),e}function aa(e){ze===null?ze=e:ze.push.apply(ze,e)}function hy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!ht(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Xt(e,t){for(t&=~uu,t&=~cl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ft(t),r=1<<n;e[n]=-1,t&=~r}}function Kc(e){if(Y&6)throw Error(L(327));sr();var t=Ri(e,0);if(!(t&1))return Ue(e,de()),null;var n=Ki(e,t);if(e.tag!==0&&n===2){var r=Ls(e);r!==0&&(t=r,n=sa(e,r))}if(n===1)throw n=Co,Cn(e,0),Xt(e,t),Ue(e,de()),n;if(n===6)throw Error(L(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wn(e,ze,At),Ue(e,de()),null}function du(e,t){var n=Y;Y|=1;try{return e(t)}finally{Y=n,Y===0&&(yr=de()+500,ll&&mn())}}function Nn(e){Jt!==null&&Jt.tag===0&&!(Y&6)&&sr();var t=Y;Y|=1;var n=rt.transition,r=Z;try{if(rt.transition=null,Z=1,e)return e()}finally{Z=r,rt.transition=n,Y=t,!(Y&6)&&mn()}}function fu(){Ve=qn.current,oe(qn)}function Cn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Hv(n)),fe!==null)for(n=fe.return;n!==null;){var r=n;switch(Ka(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Li();break;case 3:gr(),oe(Fe),oe(Re),tu();break;case 5:eu(r);break;case 4:gr();break;case 13:oe(le);break;case 19:oe(le);break;case 10:Xa(r.type._context);break;case 22:case 23:fu()}n=n.return}if(we=e,fe=e=sn(e.current,null),_e=Ve=t,ge=0,Co=null,uu=cl=Rn=0,ze=to=null,xn!==null){for(t=0;t<xn.length;t++)if(n=xn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=o,r.next=l}n.pending=r}xn=null}return e}function Mp(e,t){do{var n=fe;try{if(Ya(),fi.current=$i,Bi){for(var r=se.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Bi=!1}if(Pn=0,ye=me=se=null,qr=!1,So=0,au.current=null,n===null||n.return===null){ge=1,Co=t,fe=null;break}e:{var i=e,l=n.return,s=n,a=t;if(t=_e,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=s,m=d.tag;if(!(d.mode&1)&&(m===0||m===11||m===15)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var y=Lc(l);if(y!==null){y.flags&=-257,Dc(y,l,s,i,t),y.mode&1&&Oc(i,u,t),t=y,a=u;var w=t.updateQueue;if(w===null){var f=new Set;f.add(a),t.updateQueue=f}else w.add(a);break e}else{if(!(t&1)){Oc(i,u,t),pu();break e}a=Error(L(426))}}else if(ie&&s.mode&1){var S=Lc(l);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Dc(S,l,s,i,t),Ga(vr(a,s));break e}}i=a=vr(a,s),ge!==4&&(ge=2),to===null?to=[i]:to.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=Sp(i,a,t);bc(i,p);break e;case 1:s=a;var c=i.type,v=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(on===null||!on.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=xp(i,s,t);bc(i,x);break e}}i=i.return}while(i!==null)}Up(n)}catch(C){t=C,fe===n&&n!==null&&(fe=n=n.return);continue}break}while(!0)}function Fp(){var e=Vi.current;return Vi.current=$i,e===null?$i:e}function pu(){(ge===0||ge===3||ge===2)&&(ge=4),we===null||!(Rn&268435455)&&!(cl&268435455)||Xt(we,_e)}function Ki(e,t){var n=Y;Y|=2;var r=Fp();(we!==e||_e!==t)&&(At=null,Cn(e,t));do try{my();break}catch(o){Mp(e,o)}while(!0);if(Ya(),Y=n,Vi.current=r,fe!==null)throw Error(L(261));return we=null,_e=0,ge}function my(){for(;fe!==null;)jp(fe)}function gy(){for(;fe!==null&&!Bg();)jp(fe)}function jp(e){var t=$p(e.alternate,e,Ve);e.memoizedProps=e.pendingProps,t===null?Up(e):fe=t,au.current=null}function Up(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=uy(n,t),n!==null){n.flags&=32767,fe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ge=6,fe=null;return}}else if(n=ay(n,t,Ve),n!==null){fe=n;return}if(t=t.sibling,t!==null){fe=t;return}fe=t=e}while(t!==null);ge===0&&(ge=5)}function wn(e,t,n){var r=Z,o=rt.transition;try{rt.transition=null,Z=1,vy(e,t,n,r)}finally{rt.transition=o,Z=r}return null}function vy(e,t,n,r){do sr();while(Jt!==null);if(Y&6)throw Error(L(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(L(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Zg(e,i),e===we&&(fe=we=null,_e=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Zo||(Zo=!0,Vp(Pi,function(){return sr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=rt.transition,rt.transition=null;var l=Z;Z=1;var s=Y;Y|=4,au.current=null,dy(e,n),Dp(n,e),Mv(js),Ni=!!Fs,js=Fs=null,e.current=n,fy(n),$g(),Y=s,Z=l,rt.transition=i}else e.current=n;if(Zo&&(Zo=!1,Jt=e,Wi=o),i=e.pendingLanes,i===0&&(on=null),Wg(n.stateNode),Ue(e,de()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Hi)throw Hi=!1,e=ia,ia=null,e;return Wi&1&&e.tag!==0&&sr(),i=e.pendingLanes,i&1?e===la?no++:(no=0,la=e):no=0,mn(),null}function sr(){if(Jt!==null){var e=Sf(Wi),t=rt.transition,n=Z;try{if(rt.transition=null,Z=16>e?16:e,Jt===null)var r=!1;else{if(e=Jt,Jt=null,Wi=0,Y&6)throw Error(L(331));var o=Y;for(Y|=4,M=e.current;M!==null;){var i=M,l=i.child;if(M.flags&16){var s=i.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(M=u;M!==null;){var d=M;switch(d.tag){case 0:case 11:case 15:eo(8,d,i)}var m=d.child;if(m!==null)m.return=d,M=m;else for(;M!==null;){d=M;var h=d.sibling,y=d.return;if(Tp(d),d===u){M=null;break}if(h!==null){h.return=y,M=h;break}M=y}}}var w=i.alternate;if(w!==null){var f=w.child;if(f!==null){w.child=null;do{var S=f.sibling;f.sibling=null,f=S}while(f!==null)}}M=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,M=l;else e:for(;M!==null;){if(i=M,i.flags&2048)switch(i.tag){case 0:case 11:case 15:eo(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,M=p;break e}M=i.return}}var c=e.current;for(M=c;M!==null;){l=M;var v=l.child;if(l.subtreeFlags&2064&&v!==null)v.return=l,M=v;else e:for(l=c;M!==null;){if(s=M,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:ul(9,s)}}catch(C){ue(s,s.return,C)}if(s===l){M=null;break e}var x=s.sibling;if(x!==null){x.return=s.return,M=x;break e}M=s.return}}if(Y=o,mn(),_t&&typeof _t.onPostCommitFiberRoot=="function")try{_t.onPostCommitFiberRoot(tl,e)}catch{}r=!0}return r}finally{Z=n,rt.transition=t}}return!1}function Gc(e,t,n){t=vr(n,t),t=Sp(e,t,1),e=rn(e,t,1),t=Te(),e!==null&&(No(e,1,t),Ue(e,t))}function ue(e,t,n){if(e.tag===3)Gc(e,e,n);else for(;t!==null;){if(t.tag===3){Gc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(on===null||!on.has(r))){e=vr(n,e),e=xp(t,e,1),t=rn(t,e,1),e=Te(),t!==null&&(No(t,1,e),Ue(t,e));break}}t=t.return}}function yy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Te(),e.pingedLanes|=e.suspendedLanes&n,we===e&&(_e&n)===n&&(ge===4||ge===3&&(_e&130023424)===_e&&500>de()-cu?Cn(e,0):uu|=n),Ue(e,t)}function Bp(e,t){t===0&&(e.mode&1?(t=Bo,Bo<<=1,!(Bo&130023424)&&(Bo=4194304)):t=1);var n=Te();e=Mt(e,t),e!==null&&(No(e,t,n),Ue(e,n))}function wy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Bp(e,n)}function Sy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(L(314))}r!==null&&r.delete(t),Bp(e,n)}var $p;$p=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Fe.current)Me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Me=!1,sy(e,t,n);Me=!!(e.flags&131072)}else Me=!1,ie&&t.flags&1048576&&Kf(t,zi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;hi(e,t),e=t.pendingProps;var o=pr(t,Re.current);lr(t,n),o=ru(null,t,r,e,o,n);var i=ou();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,je(r)?(i=!0,Di(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Ja(t),o.updater=al,t.stateNode=o,o._reactInternals=t,Qs(t,r,e,n),t=Zs(null,t,r,!0,i,n)):(t.tag=0,ie&&i&&Wa(t),Ae(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(hi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=_y(r),e=ut(r,e),o){case 0:t=Xs(null,t,r,e,n);break e;case 1:t=Mc(null,t,r,e,n);break e;case 11:t=Ic(null,t,r,e,n);break e;case 14:t=zc(null,t,r,ut(r.type,e),n);break e}throw Error(L(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ut(r,o),Xs(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ut(r,o),Mc(e,t,r,o,n);case 3:e:{if(kp(t),e===null)throw Error(L(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Jf(e,t),ji(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=vr(Error(L(423)),t),t=Fc(e,t,r,n,o);break e}else if(r!==o){o=vr(Error(L(424)),t),t=Fc(e,t,r,n,o);break e}else for(We=nn(t.stateNode.containerInfo.firstChild),Ke=t,ie=!0,dt=null,n=Xf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(hr(),r===o){t=Ft(e,t,n);break e}Ae(e,t,r,n)}t=t.child}return t;case 5:return qf(t),e===null&&Ws(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,l=o.children,Us(r,o)?l=null:i!==null&&Us(r,i)&&(t.flags|=32),Ep(e,t),Ae(e,t,l,n),t.child;case 6:return e===null&&Ws(t),null;case 13:return bp(e,t,n);case 4:return qa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=mr(t,null,r,n):Ae(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ut(r,o),Ic(e,t,r,o,n);case 7:return Ae(e,t,t.pendingProps,n),t.child;case 8:return Ae(e,t,t.pendingProps.children,n),t.child;case 12:return Ae(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,te(Mi,r._currentValue),r._currentValue=l,i!==null)if(ht(i.value,l)){if(i.children===o.children&&!Fe.current){t=Ft(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=Dt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Ks(i.return,n,t),s.lanes|=n;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(L(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),Ks(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}Ae(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,lr(t,n),o=ot(o),r=r(o),t.flags|=1,Ae(e,t,r,n),t.child;case 14:return r=t.type,o=ut(r,t.pendingProps),o=ut(r.type,o),zc(e,t,r,o,n);case 15:return _p(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ut(r,o),hi(e,t),t.tag=1,je(r)?(e=!0,Di(t)):e=!1,lr(t,n),wp(t,r,o),Qs(t,r,o,n),Zs(null,t,r,!0,e,n);case 19:return Pp(e,t,n);case 22:return Cp(e,t,n)}throw Error(L(156,t.tag))};function Vp(e,t){return gf(e,t)}function xy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nt(e,t,n,r){return new xy(e,t,n,r)}function hu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function _y(e){if(typeof e=="function")return hu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===La)return 11;if(e===Da)return 14}return 2}function sn(e,t){var n=e.alternate;return n===null?(n=nt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function vi(e,t,n,r,o,i){var l=2;if(r=e,typeof e=="function")hu(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case Vn:return En(n.children,o,i,t);case Oa:l=8,o|=8;break;case ys:return e=nt(12,n,t,o|2),e.elementType=ys,e.lanes=i,e;case ws:return e=nt(13,n,t,o),e.elementType=ws,e.lanes=i,e;case Ss:return e=nt(19,n,t,o),e.elementType=Ss,e.lanes=i,e;case Jd:return dl(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Xd:l=10;break e;case Zd:l=9;break e;case La:l=11;break e;case Da:l=14;break e;case Kt:l=16,r=null;break e}throw Error(L(130,e==null?e:typeof e,""))}return t=nt(l,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function En(e,t,n,r){return e=nt(7,e,r,t),e.lanes=n,e}function dl(e,t,n,r){return e=nt(22,e,r,t),e.elementType=Jd,e.lanes=n,e.stateNode={isHidden:!1},e}function Jl(e,t,n){return e=nt(6,e,null,t),e.lanes=n,e}function ql(e,t,n){return t=nt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Cy(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ll(0),this.expirationTimes=Ll(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ll(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function mu(e,t,n,r,o,i,l,s,a){return e=new Cy(e,t,n,s,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=nt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ja(i),e}function Ey(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:$n,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Hp(e){if(!e)return un;e=e._reactInternals;e:{if(In(e)!==e||e.tag!==1)throw Error(L(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(L(171))}if(e.tag===1){var n=e.type;if(je(n))return Hf(e,n,t)}return t}function Wp(e,t,n,r,o,i,l,s,a){return e=mu(n,r,!0,e,o,i,l,s,a),e.context=Hp(null),n=e.current,r=Te(),o=ln(n),i=Dt(r,o),i.callback=t??null,rn(n,i,o),e.current.lanes=o,No(e,o,r),Ue(e,r),e}function fl(e,t,n,r){var o=t.current,i=Te(),l=ln(o);return n=Hp(n),t.context===null?t.context=n:t.pendingContext=n,t=Dt(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=rn(o,t,l),e!==null&&(pt(e,o,l,i),di(e,o,l)),l}function Gi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Qc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function gu(e,t){Qc(e,t),(e=e.alternate)&&Qc(e,t)}function ky(){return null}var Kp=typeof reportError=="function"?reportError:function(e){console.error(e)};function vu(e){this._internalRoot=e}pl.prototype.render=vu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(L(409));fl(e,t,null,null)};pl.prototype.unmount=vu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Nn(function(){fl(null,e,null,null)}),t[zt]=null}};function pl(e){this._internalRoot=e}pl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Cf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Yt.length&&t!==0&&t<Yt[n].priority;n++);Yt.splice(n,0,e),n===0&&kf(e)}};function yu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function hl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Yc(){}function by(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Gi(l);i.call(u)}}var l=Wp(t,r,e,0,null,!1,!1,"",Yc);return e._reactRootContainer=l,e[zt]=l.current,mo(e.nodeType===8?e.parentNode:e),Nn(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=Gi(a);s.call(u)}}var a=mu(e,0,!1,null,null,!1,!1,"",Yc);return e._reactRootContainer=a,e[zt]=a.current,mo(e.nodeType===8?e.parentNode:e),Nn(function(){fl(t,a,n,r)}),a}function ml(e,t,n,r,o){var i=n._reactRootContainer;if(i){var l=i;if(typeof o=="function"){var s=o;o=function(){var a=Gi(l);s.call(a)}}fl(t,l,e,o)}else l=by(n,t,e,o,r);return Gi(l)}xf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Wr(t.pendingLanes);n!==0&&(Ma(t,n|1),Ue(t,de()),!(Y&6)&&(yr=de()+500,mn()))}break;case 13:Nn(function(){var r=Mt(e,1);if(r!==null){var o=Te();pt(r,e,1,o)}}),gu(e,1)}};Fa=function(e){if(e.tag===13){var t=Mt(e,134217728);if(t!==null){var n=Te();pt(t,e,134217728,n)}gu(e,134217728)}};_f=function(e){if(e.tag===13){var t=ln(e),n=Mt(e,t);if(n!==null){var r=Te();pt(n,e,t,r)}gu(e,t)}};Cf=function(){return Z};Ef=function(e,t){var n=Z;try{return Z=e,t()}finally{Z=n}};As=function(e,t,n){switch(t){case"input":if(Cs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=il(r);if(!o)throw Error(L(90));ef(r),Cs(r,o)}}}break;case"textarea":nf(e,n);break;case"select":t=n.value,t!=null&&nr(e,!!n.multiple,t,!1)}};cf=du;df=Nn;var Py={usingClientEntryPoint:!1,Events:[To,Gn,il,af,uf,du]},jr={findFiberByHostInstance:Sn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ry={bundleType:jr.bundleType,version:jr.version,rendererPackageName:jr.rendererPackageName,rendererConfig:jr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Bt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=hf(e),e===null?null:e.stateNode},findFiberByHostInstance:jr.findFiberByHostInstance||ky,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Jo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Jo.isDisabled&&Jo.supportsFiber)try{tl=Jo.inject(Ry),_t=Jo}catch{}}Ye.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Py;Ye.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!yu(t))throw Error(L(200));return Ey(e,t,null,n)};Ye.createRoot=function(e,t){if(!yu(e))throw Error(L(299));var n=!1,r="",o=Kp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=mu(e,1,!1,null,null,n,!1,r,o),e[zt]=t.current,mo(e.nodeType===8?e.parentNode:e),new vu(t)};Ye.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(L(188)):(e=Object.keys(e).join(","),Error(L(268,e)));return e=hf(t),e=e===null?null:e.stateNode,e};Ye.flushSync=function(e){return Nn(e)};Ye.hydrate=function(e,t,n){if(!hl(t))throw Error(L(200));return ml(null,e,t,!0,n)};Ye.hydrateRoot=function(e,t,n){if(!yu(e))throw Error(L(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",l=Kp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=Wp(t,null,e,1,n??null,o,!1,i,l),e[zt]=t.current,mo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new pl(t)};Ye.render=function(e,t,n){if(!hl(t))throw Error(L(200));return ml(null,e,t,!1,n)};Ye.unmountComponentAtNode=function(e){if(!hl(e))throw Error(L(40));return e._reactRootContainer?(Nn(function(){ml(null,null,e,!1,function(){e._reactRootContainer=null,e[zt]=null})}),!0):!1};Ye.unstable_batchedUpdates=du;Ye.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!hl(n))throw Error(L(200));if(e==null||e._reactInternals===void 0)throw Error(L(38));return ml(e,t,n,!1,r)};Ye.version="18.3.1-next-f1338f8080-20240426";function Gp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Gp)}catch(e){console.error(e)}}Gp(),Kd.exports=Ye;var Er=Kd.exports;const Ny=Ld(Er);var Xc=Er;Hu.createRoot=Xc.createRoot,Hu.hydrateRoot=Xc.hydrateRoot;function Ie(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)}function yi(e,t,n,r,o){if(typeof t=="function"?e!==t||!o:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}var Mn,yt,Fn,wi;const Be="__TAURI_TO_IPC_KEY__";function Qp(e,t=!1){return window.__TAURI_INTERNALS__.transformCallback(e,t)}class Dx{constructor(){this.__TAURI_CHANNEL_MARKER__=!0,Mn.set(this,()=>{}),yt.set(this,0),Fn.set(this,[]),this.id=Qp(({message:t,id:n})=>{if(n==Ie(this,yt,"f"))for(Ie(this,Mn,"f").call(this,t),yi(this,yt,Ie(this,yt,"f")+1);Ie(this,yt,"f")in Ie(this,Fn,"f");){const r=Ie(this,Fn,"f")[Ie(this,yt,"f")];Ie(this,Mn,"f").call(this,r),delete Ie(this,Fn,"f")[Ie(this,yt,"f")],yi(this,yt,Ie(this,yt,"f")+1)}else Ie(this,Fn,"f")[n]=t})}set onmessage(t){yi(this,Mn,t)}get onmessage(){return Ie(this,Mn,"f")}[(Mn=new WeakMap,yt=new WeakMap,Fn=new WeakMap,Be)](){return`__CHANNEL__:${this.id}`}toJSON(){return this[Be]()}}async function T(e,t={},n){return window.__TAURI_INTERNALS__.invoke(e,t,n)}class Yp{get rid(){return Ie(this,wi,"f")}constructor(t){wi.set(this,void 0),yi(this,wi,t)}async close(){return T("plugin:resources|close",{rid:this.rid})}}wi=new WeakMap;var ro=(e=>(e[e.Byte=0]="Byte",e[e.Short=1]="Short",e[e.Int=2]="Int",e[e.Float=3]="Float",e[e.Buffer=4]="Buffer",e[e.SignedShort=5]="SignedShort",e[e.SignedInt=6]="SignedInt",e))(ro||{});function Si(e,t){return new Promise((n,r)=>{const i=["read_memory_byte","read_memory_short","read_memory_int","read_memory_float","read_memory_buffer","read_memory_signed_short","read_memory_signed_int"][t];T(i,{address:e}).then(l=>{n(l)}).catch(l=>{console.error("Error reading memory",l),r(l)})})}function Ay(e,t){return new Promise((n,r)=>{T("read_memory_buffer",{address:e,size:t}).then(o=>{n(o)}).catch(o=>{console.error("Error reading memory buffer",o),r(o)})})}function Ix(e,t,n){return new Promise((r,o)=>{const l=["write_memory_byte","write_memory_short","write_memory_int","write_memory_float","write_memory_buffer","write_memory_signed_short","write_memory_signed_int"][n],s={address:e};n===4?s.buffer=t:s.newValue=t,T(l,s).then(()=>{r()}).catch(a=>{console.error("Error writing memory",a),o(a)})})}function zx(e,t){return new Promise((n,r)=>{T("set_memory_protection",{address:e,size:t}).then(()=>{n()}).catch(o=>{console.error("Error setting memory protection",o),r(o)})})}const $e={Dead:1,NearDeath:2,Sleep:4,Poison:8,Sadness:16,Fury:32,Confusion:64,Silence:128,Haste:256,Slow:512,Stop:1024,Frog:2048,Small:4096,SlowNumb:8192,Petrify:16384,Regen:32768,Barrier:65536,MBarrier:131072,Reflect:262144,Dual:524288,Shield:1048576,DeathSentence:2097152,Berserk:8388608,Peerless:16777216,Paralyze:33554432,Darkness:67108864,DualDrain:134217728,DeathForce:268435456,Resist:536870912,LuckyGirl:1073741824,Imprisoned:2147483648},Mx=$e.Sadness|$e.Haste|$e.Regen|$e.Barrier|$e.MBarrier|$e.Reflect|$e.Shield|$e.Berserk|$e.Peerless|$e.Resist|$e.LuckyGirl;var Ty=(e=>(e.General="general",e.Field="field",e.World="world",e.Battle="battle",e.Party="party",e.Chocobos="chocobos",e.Variables="variables",e))(Ty||{}),st=(e=>(e[e.None=0]="None",e[e.Field=1]="Field",e[e.Battle=2]="Battle",e[e.World=3]="World",e[e.Menu=5]="Menu",e[e.Highway=6]="Highway",e[e.Chocobo=7]="Chocobo",e[e.SnowBoard=8]="SnowBoard",e[e.Condor=9]="Condor",e[e.Submarine=10]="Submarine",e[e.Jet=11]="Jet",e[e.ChangeDisc=12]="ChangeDisc",e[e.Snowboard2=14]="Snowboard2",e[e.Quit=19]="Quit",e[e.Start=20]="Start",e[e.BattleSwirl=23]="BattleSwirl",e[e.Ending=25]="Ending",e[e.GameOver=26]="GameOver",e[e.Intro=27]="Intro",e[e.Credits=28]="Credits",e))(st||{}),oo=(e=>(e[e.Off=0]="Off",e[e.Normal=1]="Normal",e[e.Max=2]="Max",e))(oo||{}),Oy=(e=>(e[e.Cloud=0]="Cloud",e[e.Tifa=1]="Tifa",e[e.Cid=2]="Cid",e[e.Highwind=3]="Highwind",e[e.WildChocobo=4]="WildChocobo",e[e.TinyBronco=5]="TinyBronco",e[e.Buggy=6]="Buggy",e[e.JunonCanon=7]="JunonCanon",e[e.CargoShip=8]="CargoShip",e[e.HighwindPropellers=9]="HighwindPropellers",e[e.DiamondWeapon=10]="DiamondWeapon",e[e.UltimateWeapon=11]="UltimateWeapon",e[e.FortCondor=12]="FortCondor",e[e.Submarine=13]="Submarine",e[e.GoldSaucer=14]="GoldSaucer",e[e.RocketTownRocket=15]="RocketTownRocket",e[e.RocketTownPad=16]="RocketTownPad",e[e.SunkenGelnika=17]="SunkenGelnika",e[e.UnderwaterReactor=18]="UnderwaterReactor",e[e.Chocobo=19]="Chocobo",e[e.MidgarCanon=20]="MidgarCanon",e[e.Unknown1=21]="Unknown1",e[e.Unknown2=22]="Unknown2",e[e.Unknown3=23]="Unknown3",e[e.NorthCraterBarrier=24]="NorthCraterBarrier",e[e.AncientForest=25]="AncientForest",e[e.KeyOfTheAncients=26]="KeyOfTheAncients",e[e.Unknown4=27]="Unknown4",e[e.RedSubmarine=28]="RedSubmarine",e[e.RubyWeapon=29]="RubyWeapon",e[e.EmeraldWeapon=30]="EmeraldWeapon",e))(Oy||{}),Ly=(e=>(e[e.Grass=0]="Grass",e[e.Forest=1]="Forest",e[e.Mountain=2]="Mountain",e[e.Sea=3]="Sea",e[e.RiverCrossing=4]="RiverCrossing",e[e.River=5]="River",e[e.Water=6]="Water",e[e.Swamp=7]="Swamp",e[e.Desert=8]="Desert",e[e.Wasteland=9]="Wasteland",e[e.Snow=10]="Snow",e[e.Riverside=11]="Riverside",e[e.Cliff=12]="Cliff",e[e.CorelBridge=13]="CorelBridge",e[e.WutaiBridge=14]="WutaiBridge",e[e.Unused1=15]="Unused1",e[e.Hillside=16]="Hillside",e[e.Beach=17]="Beach",e[e.SubPen=18]="SubPen",e[e.Canyon=19]="Canyon",e[e.MountainPass=20]="MountainPass",e[e.UnknownCliff=21]="UnknownCliff",e[e.Waterfall=22]="Waterfall",e[e.Unused2=23]="Unused2",e[e.SaucerDesert=24]="SaucerDesert",e[e.Jungle=25]="Jungle",e[e.Sea2=26]="Sea2",e[e.NorthernCave=27]="NorthernCave",e[e.DesertBorder=28]="DesertBorder",e[e.Bridgehead=29]="Bridgehead",e[e.BackEntrance=30]="BackEntrance",e[e.Unused3=31]="Unused3",e))(Ly||{}),Xp=(e=>(e[e.Fire=0]="Fire",e[e.Ice=1]="Ice",e[e.Lightning=2]="Lightning",e[e.Earth=3]="Earth",e[e.Poison=4]="Poison",e[e.Gravity=5]="Gravity",e[e.Water=6]="Water",e[e.Wind=7]="Wind",e[e.Holy=8]="Holy",e[e.Restorative=9]="Restorative",e[e.Cut=10]="Cut",e[e.Hit=11]="Hit",e[e.Punch=12]="Punch",e[e.Shoot=13]="Shoot",e[e.Scream=14]="Scream",e[e.Hidden=15]="Hidden",e[e.Nothing=255]="Nothing",e))(Xp||{}),Dy=(e=>(e[e.Death=0]="Death",e[e.AutoHit=1]="AutoHit",e[e.DoubleDamage=2]="DoubleDamage",e[e.HalfDamage=4]="HalfDamage",e[e.Nullify=5]="Nullify",e[e.Absorb=6]="Absorb",e[e.FullCure=7]="FullCure",e[e.Nothing=255]="Nothing",e))(Dy||{}),Iy=(e=>(e[e["Midgar Area"]=0]="Midgar Area",e[e["Grasslands Area"]=1]="Grasslands Area",e[e["Junon Area"]=2]="Junon Area",e[e["Corel Area"]=3]="Corel Area",e[e["Gold Saucer Area"]=4]="Gold Saucer Area",e[e["Gongaga Area"]=5]="Gongaga Area",e[e["Cosmo Area"]=6]="Cosmo Area",e[e["Nibel Area"]=7]="Nibel Area",e[e["Rocket Launch Pad Area"]=8]="Rocket Launch Pad Area",e[e["Wutai Area"]=9]="Wutai Area",e[e["Woodlands Area"]=10]="Woodlands Area",e[e["Icicle Area"]=11]="Icicle Area",e[e["Mideel Area"]=12]="Mideel Area",e[e["North Corel Area"]=13]="North Corel Area",e[e["Cactus Island"]=14]="Cactus Island",e[e["Goblin Island"]=15]="Goblin Island",e[e["Round Island"]=16]="Round Island",e[e.Sea=17]="Sea",e[e["Bottom of the Sea"]=18]="Bottom of the Sea",e[e.Glacier=19]="Glacier",e))(Iy||{}),zy=(e=>(e[e.wonderful=1]="wonderful",e[e.great=2]="great",e[e.good=3]="good",e[e["so-so"]=4]="so-so",e[e.average=5]="average",e[e["not bad"]=6]="not bad",e[e.bad=7]="bad",e[e.terrible=8]="terrible",e))(zy||{});function My(){const[e,t]=g.useState(null),[n,r]=g.useState(!0),[o,i]=g.useState(null);return g.useEffect(()=>{async function l(){try{const s=await T("get_ff7_addresses");t(s),r(!1)}catch(s){i(s instanceof Error?s.message:"An unknown error occurred"),r(!1)}}l()},[]),{addresses:e,isLoading:n,error:o}}var er={BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};const Zc=(e,t)=>e.unstable_is?e.unstable_is(t):t===e,Jc=e=>"init"in e,es=e=>!!e.write,qc=e=>"v"in e||"e"in e,qo=e=>{if("e"in e)throw e.e;if((er?"production":void 0)!=="production"&&!("v"in e))throw new Error("[Bug] atom state is not initialized");return e.v},Zp=Symbol(),wu=e=>e[Zp],ua=e=>{var t;return Su(e)&&!((t=wu(e))!=null&&t[1])},Fy=(e,t)=>{const n=wu(e);if(n)n[1]=!0,n[0].forEach(r=>r(t));else if((er?"production":void 0)!=="production")throw new Error("[Bug] cancelable promise not found")},jy=e=>{if(wu(e))return;const t=[new Set,!1];e[Zp]=t;const n=()=>{t[1]=!0};e.then(n,n),e.onCancel=r=>{t[0].add(r)}},Su=e=>typeof(e==null?void 0:e.then)=="function",Jp=(e,t,n)=>{n.p.has(e)||(n.p.add(e),t.then(()=>{n.p.delete(e)},()=>{n.p.delete(e)}))},ts=(e,t,n)=>{const r=n(e),o="v"in r,i=r.v,l=ua(r.v)?r.v:null;if(Su(t)){jy(t);for(const s of r.d.keys())Jp(e,t,n(s))}r.v=t,delete r.e,(!o||!Object.is(i,r.v))&&(++r.n,l&&Fy(l,t))},ed=(e,t,n)=>{var r;const o=new Set;for(const i of((r=n.get(e))==null?void 0:r.t)||[])n.has(i)&&o.add(i);for(const i of t.p)o.add(i);return o},Uy=()=>{const e=new Set,t=()=>{e.forEach(n=>n())};return t.add=n=>(e.add(n),()=>{e.delete(n)}),t},ns=()=>{const e={},t=new WeakMap,n=r=>{var o,i;(o=t.get(e))==null||o.forEach(l=>l(r)),(i=t.get(r))==null||i.forEach(l=>l())};return n.add=(r,o)=>{const i=r||e,l=(t.has(i)?t:t.set(i,new Set)).get(i);return l.add(o),()=>{l==null||l.delete(o),l.size||t.delete(i)}},n},By=e=>(e.c||(e.c=ns()),e.m||(e.m=ns()),e.u||(e.u=ns()),e.f||(e.f=Uy()),e),$y=Symbol(),Vy=(e=new WeakMap,t=new WeakMap,n=new WeakMap,r=new Set,o=new Set,i=new Set,l={},s=(h,...y)=>h.read(...y),a=(h,...y)=>h.write(...y),u=(h,y)=>{var w;return(w=h.unstable_onInit)==null?void 0:w.call(h,y)},d=(h,y)=>{var w;return(w=h.onMount)==null?void 0:w.call(h,y)},...m)=>{const h=m[0]||(_=>{if((er?"production":void 0)!=="production"&&!_)throw new Error("Atom is undefined or null");let P=e.get(_);return P||(P={d:new Map,p:new Set,n:0},e.set(_,P),u==null||u(_,R)),P}),y=m[1]||(()=>{let _,P;const z=E=>{try{E()}catch(O){_||(_=!0,P=O)}};do{l.f&&z(l.f);const E=new Set,O=E.add.bind(E);r.forEach(N=>{var D;return(D=t.get(N))==null?void 0:D.l.forEach(O)}),r.clear(),i.forEach(O),i.clear(),o.forEach(O),o.clear(),E.forEach(z),r.size&&w()}while(r.size||i.size||o.size);if(_)throw P}),w=m[2]||(()=>{var _;const P=[],z=new WeakSet,E=new WeakSet,O=Array.from(r);for(;O.length;){const N=O[O.length-1],D=h(N);if(E.has(N)){O.pop();continue}if(z.has(N)){if(n.get(N)===D.n)P.push([N,D,D.n]);else if((er?"production":void 0)!=="production"&&n.has(N))throw new Error("[Bug] invalidated atom exists");E.add(N),O.pop();continue}z.add(N);for(const k of ed(N,D,t))z.has(k)||O.push(k)}for(let N=P.length-1;N>=0;--N){const[D,k,U]=P[N];let j=!1;for(const $ of k.d.keys())if($!==D&&r.has($)){j=!0;break}j&&(f(D),c(D),U!==k.n&&(r.add(D),(_=l.c)==null||_.call(l,D))),n.delete(D)}}),f=m[3]||(_=>{var P,z;const E=h(_);if(qc(E)&&(t.has(_)&&n.get(_)!==E.n||Array.from(E.d).every(([b,I])=>f(b).n===I)))return E;E.d.clear();let O=!0;const N=()=>{t.has(_)&&(c(_),w(),y())},D=b=>{var I;if(Zc(_,b)){const B=h(b);if(!qc(B))if(Jc(b))ts(b,b.init,h);else throw new Error("no atom init");return qo(B)}const F=f(b);try{return qo(F)}finally{E.d.set(b,F.n),ua(E.v)&&Jp(_,E.v,F),(I=t.get(b))==null||I.t.add(_),O||N()}};let k,U;const j={get signal(){return k||(k=new AbortController),k.signal},get setSelf(){return(er?"production":void 0)!=="production"&&!es(_)&&console.warn("setSelf function cannot be used with read-only atom"),!U&&es(_)&&(U=(...b)=>{if((er?"production":void 0)!=="production"&&O&&console.warn("setSelf function cannot be called in sync"),!O)try{return p(_,...b)}finally{w(),y()}}),U}},$=E.n;try{const b=s(_,D,j);return ts(_,b,h),Su(b)&&((P=b.onCancel)==null||P.call(b,()=>k==null?void 0:k.abort()),b.then(N,N)),E}catch(b){return delete E.v,E.e=b,++E.n,E}finally{O=!1,$!==E.n&&n.get(_)===$&&(n.set(_,E.n),r.add(_),(z=l.c)==null||z.call(l,_))}}),S=m[4]||(_=>{const P=[_];for(;P.length;){const z=P.pop(),E=h(z);for(const O of ed(z,E,t)){const N=h(O);n.set(O,N.n),P.push(O)}}}),p=m[5]||((_,...P)=>{let z=!0;const E=N=>qo(f(N)),O=(N,...D)=>{var k;const U=h(N);try{if(Zc(_,N)){if(!Jc(N))throw new Error("atom not writable");const j=U.n,$=D[0];ts(N,$,h),c(N),j!==U.n&&(r.add(N),(k=l.c)==null||k.call(l,N),S(N));return}else return p(N,...D)}finally{z||(w(),y())}};try{return a(_,E,O,...P)}finally{z=!1}}),c=m[6]||(_=>{var P;const z=h(_),E=t.get(_);if(E&&!ua(z.v)){for(const[O,N]of z.d)if(!E.d.has(O)){const D=h(O);v(O).t.add(_),E.d.add(O),N!==D.n&&(r.add(O),(P=l.c)==null||P.call(l,O),S(O))}for(const O of E.d||[])if(!z.d.has(O)){E.d.delete(O);const N=x(O);N==null||N.t.delete(_)}}}),v=m[7]||(_=>{var P;const z=h(_);let E=t.get(_);if(!E){f(_);for(const O of z.d.keys())v(O).t.add(_);if(E={l:new Set,d:new Set(z.d.keys()),t:new Set},t.set(_,E),(P=l.m)==null||P.call(l,_),es(_)){const O=()=>{let N=!0;const D=(...k)=>{try{return p(_,...k)}finally{N||(w(),y())}};try{const k=d(_,D);k&&(E.u=()=>{N=!0;try{k()}finally{N=!1}})}finally{N=!1}};o.add(O)}}return E}),x=m[8]||(_=>{var P;const z=h(_);let E=t.get(_);if(E&&!E.l.size&&!Array.from(E.t).some(O=>{var N;return(N=t.get(O))==null?void 0:N.d.has(_)})){E.u&&i.add(E.u),E=void 0,t.delete(_),(P=l.u)==null||P.call(l,_);for(const O of z.d.keys()){const N=x(O);N==null||N.t.delete(_)}return}return E}),C=[e,t,n,r,o,i,l,s,a,u,d,h,y,w,f,S,p,c,v,x],R={get:_=>qo(f(_)),set:(_,...P)=>{try{return p(_,...P)}finally{w(),y()}},sub:(_,P)=>{const E=v(_).l;return E.add(P),y(),()=>{E.delete(P),x(_),y()}}};return Object.defineProperty(R,$y,{value:C}),R},qp=Vy,Hy=By;var xu={BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};let Wy=0;function Ky(e,t){const n=`atom${++Wy}`,r={toString(){return(xu?"production":void 0)!=="production"&&this.debugLabel?n+":"+this.debugLabel:n}};return typeof e=="function"?r.read=e:(r.init=e,r.read=Gy,r.write=Qy),r}function Gy(e){return e(this)}function Qy(e,t,n){return t(this,typeof n=="function"?n(e(this)):n)}const Yy=()=>{let e=0;const t=Hy({}),n=new WeakMap,r=new WeakMap,o=qp(n,r,void 0,void 0,void 0,void 0,t,void 0,(s,a,u,...d)=>e?u(s,...d):s.write(a,u,...d)),i=new Set;return t.m.add(void 0,s=>{i.add(s);const a=n.get(s);a.m=r.get(s)}),t.u.add(void 0,s=>{i.delete(s);const a=n.get(s);delete a.m}),Object.assign(o,{dev4_get_internal_weak_map:()=>n,dev4_get_mounted_atoms:()=>i,dev4_restore_atoms:s=>{const a={read:()=>null,write:(u,d)=>{++e;try{for(const[m,h]of s)"init"in m&&d(m,h)}finally{--e}}};o.set(a)}})},Xy=()=>(xu?"production":void 0)!=="production"?Yy():qp();let Ur;const Zy=()=>(Ur||(Ur=Xy(),(xu?"production":void 0)!=="production"&&(globalThis.__JOTAI_DEFAULT_STORE__||(globalThis.__JOTAI_DEFAULT_STORE__=Ur),globalThis.__JOTAI_DEFAULT_STORE__!==Ur&&console.warn("Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044"))),Ur);var eh={BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};const Jy=g.createContext(void 0),th=e=>g.useContext(Jy)||Zy(),nh=e=>typeof(e==null?void 0:e.then)=="function",qy=e=>{e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t})},ew=Nt.use||(e=>{if(e.status==="pending")throw e;if(e.status==="fulfilled")return e.value;throw e.status==="rejected"?e.reason:(qy(e),e)}),rs=new WeakMap,tw=e=>{let t=rs.get(e);return t||(t=new Promise((n,r)=>{let o=e;const i=a=>u=>{o===a&&n(u)},l=a=>u=>{o===a&&r(u)},s=a=>{"onCancel"in a&&typeof a.onCancel=="function"&&a.onCancel(u=>{if((eh?"production":void 0)!=="production"&&u===a)throw new Error("[Bug] p is not updated even after cancelation");nh(u)?(rs.set(u,t),o=u,u.then(i(u),l(u)),s(u)):n(u)})};e.then(i(e),l(e)),s(e)}),rs.set(e,t)),t};function nw(e,t){const n=th(),[[r,o,i],l]=g.useReducer(u=>{const d=n.get(e);return Object.is(u[0],d)&&u[1]===n&&u[2]===e?u:[d,n,e]},void 0,()=>[n.get(e),n,e]);let s=r;if((o!==n||i!==e)&&(l(),s=n.get(e)),g.useEffect(()=>{const u=n.sub(e,()=>{l()});return l(),u},[n,e,void 0]),g.useDebugValue(s),nh(s)){const u=tw(s);return ew(u)}return s}function rh(e,t){const n=th();return g.useCallback((...o)=>{if((eh?"production":void 0)!=="production"&&!("write"in e))throw new Error("not writable atom");return n.set(e,...o)},[n,e])}function rw(e,t){return[nw(e),rh(e)]}const oh=Ky([]);function Fx(){const{addresses:e,gameState:t,gameData:n,isLoadingGameData:r}=lw(),[o,i]=rw(oh);return{logs:o,addLogItem:async y=>{if(!e||y.commandId!==void 0&&y.commandId==255)return;const w=await Si(e.battle_obj_ptr,ro.Int),f=await Ay(w,612),S=new DataView(new Uint8Array(f).buffer),p=S.getInt32(532,!0),c=S.getUint8(536)&1,v=S.getUint8(544)&2,x={...y,timestamp:Date.now(),damage:p,miss:!!c,crit:!!v};i(C=>C.some(_=>_.queuePosition!==void 0&&_.priority!==void 0&&x.queuePosition!==void 0&&x.priority!==void 0&&_.queuePosition===x.queuePosition&&_.priority===x.priority)?C:(console.debug("Adding new battle log item",x),[...C,x]))},hasLogItem:(y,w)=>o.some(f=>f.queuePosition!==void 0&&f.priority!==void 0&&f.queuePosition===y&&f.priority===w),formatCommand:(y,w)=>{if(y===void 0||w===void 0)return"Unknown Event";if(r)return"Loading names...";const f=n.commandNames,S=n.magicNames,p=n.summonNames,c=n.itemNames,v=n.enemySkillNames,x=n.enemyAttackNames,C=f[y]||`Unknown Cmd ${y.toString(16).toUpperCase()}`;switch(y){case 2:return`${S[w]||`Unknown Magic ${w}`}`;case 3:return`${p[w]||`Unknown Summon ${w}`}`;case 4:return`${c[w]||`Unknown Item ${w}`}`;case 13:return`${C}: ${v[w]||`Unknown E.Skill ${w}`}`;case 32:return x[w]||`Unknown Enemy Attack ${w.toString(16).toUpperCase()}`;case 35:return"Poison";default:return C}},getDamageType:(y,w)=>{if(y===void 0||w===void 0||r||!(n!=null&&n.itemData))return"HP";if(y===4){const f=n.itemData[w];if(f&&f.special_attack_flags&ih.DamageMP)return"MP"}return"HP"},isRestorative:(y,w)=>{if(y===void 0||w===void 0||r||!(n!=null&&n.itemData))return!1;if(y===4){const f=n.itemData[w];return f?(f.attack_element&1<<Xp.Restorative)!==0:!1}return!1},addStatusChangeItem:(y,w)=>{if(w.length===0)return;const f={targetCharacterIndex:y,changedStatuses:w,timestamp:Date.now()};console.debug("Adding status change log item",f),i(S=>[...S,f])},isLoadingNames:r,clearLogs:()=>{i([])}}}var ih=(e=>(e[e.DamageMP=1]="DamageMP",e[e.Unknown_2=2]="Unknown_2",e[e.AffectedByDarkness=4]="AffectedByDarkness",e[e.DrainsSomeHP=16]="DrainsSomeHP",e[e.DrainsSomeHPMP=32]="DrainsSomeHPMP",e))(ih||{});const ow={commandNames:[],magicNames:[],summonNames:[],enemySkillNames:[],enemyAttackNames:[],itemData:[],itemNames:[],materiaNames:[],battleScenes:[]},iw={currentModule:0,gameMoment:0,fieldId:0,fieldName:"",fieldFps:0,battleFps:0,worldFps:0,inGameTime:0,discId:0,menuVisibility:0,menuLocks:0,fieldMovementDisabled:0,fieldMenuAccessEnabled:0,fieldSkipDialoguesEnabled:!1,partyLockingBitmask:0,partyVisibilityBitmask:0,gil:0,gp:0,battleCount:0,battleEscapeCount:0,battlesDisabled:!1,maxBattlesEnabled:!1,gameObjPtr:0,battleSwirlDisabled:!1,instantATBEnabled:!1,manualSlotsEnabled:!1,slotsActive:0,fieldCurrentModelId:0,fieldModels:[],battleAllies:[],battleEnemies:[],speed:"",unfocusPatchEnabled:!1,isFFnx:!1,stepId:0,stepOffset:0,stepFraction:0,dangerValue:0,formationIndex:0,battleId:0,invincibilityEnabled:!1,worldCurrentModel:{},expMultiplier:1,apMultiplier:1,gilMultiplier:1,randomEncounters:oo.Normal,worldModels:[],battleChocoboRating:0,menuAlwaysEnabled:!1,worldZoomTiltEnabled:!1,worldZoom:0,worldTilt:0,worldSpeedMultiplier:2,partyMemberIds:[],keyItems:[],partyMembers:[],zolomCoords:null,worldMapType:0,fieldTmpVars:[],fieldLines:[],battleQueue:[],walkAnywhereEnabled:!1,lovePoints:[],battlePoints:0,chocoboData:null},lh=g.createContext(void 0),jx=({children:e})=>{const{addresses:t,isLoading:n,error:r}=My(),o=rh(oh),[i,l]=g.useState(!1),[s,a]=g.useState(iw),[u,d]=g.useState(ow),[m,h]=g.useState(!0),[y,w]=g.useState({skipIntro:!1}),f=g.useRef({}),S=g.useRef(null),[p,c]=g.useState(null),[v,x]=g.useState(0),C=(E,O)=>{if(O.length===0)return;const N={targetCharacterIndex:E,changedStatuses:O,timestamp:Date.now()};console.debug("Adding status change log item (from context)",N),o(D=>[...D,N])},R=(E,O)=>{const N=f.current[O];if(!E||N!==void 0&&E.status===N)return;const D=E.status??0,k=N??0,U=[];for(const j in $e){const $=$e[j],b=(k&$)!==0,I=(D&$)!==0;b!==I&&U.push({statusId:j,inflicted:I})}U.length>0&&(console.log(`Status change detected for index ${O}:`,U),C(O,U))},_=async()=>{if(!(!i||s.currentModule===st.None))try{console.debug("Loading core game data...",s.currentModule),h(!0),c(null);const E=await T("read_command_names"),O=await T("read_attack_names"),N=await T("read_item_data"),D=await T("read_item_names"),k=await T("read_materia_names"),U=await T("read_battle_scenes"),j=O.slice(0,56),$=O.slice(56,72),b=O.slice(72,96);d(I=>({...I,commandNames:E,magicNames:j,summonNames:$,enemySkillNames:b,itemData:N,itemNames:D,materiaNames:k,battleScenes:U})),c(null),console.debug("Core game data loaded.")}catch(E){c(E),console.error("Failed to load core game data:",E)}finally{h(!1)}};g.useEffect(()=>{if(!i)return;let E=null,O=!1;return(async()=>{await _(),!O&&i&&p&&(E=setTimeout(()=>{x(D=>D+1)},5e3))})(),()=>{O=!0,E&&clearTimeout(E)}},[i,v]),g.useEffect(()=>{i||(x(0),c(null))},[i]),g.useEffect(()=>{if(n||r||!t){l(!1),f.current={};return}const E=D=>{const k=[];for(let U=0;U<64;U++)D[Math.floor(U/8)]&1<<U%8&&k.push(U);return k},O=async()=>{if(!t){l(!1),f.current={};return}try{const D=await T("read_ff7_data"),k=D.basic,U=k.current_module;U===st.Battle&&S.current!==st.Battle&&(console.debug("Transitioned into Battle module. Clearing previous statuses ref."),f.current={});const j=D.field_data,$=k.ffnx_check===233,b=k.current_module===st.Field?k.field_fps:k.current_module===st.Battle?k.battle_fps:k.world_fps,I=k.current_module===st.Field?30:k.current_module===st.Battle?15:30;let F=Math.floor(1e7/b/I*100)/100;if(Math.round(F*10)===10&&(F=1),$){if(!t){l(!1);return}try{const G=await Si(t.ffnx_check+1,ro.Int)+t.ffnx_check+5,W=await Si(G+10,ro.Int),H=await Si(W,ro.Float);F=Math.floor(H/30*100)/100}catch(G){console.warn("Failed to read FFnx speed:",G)}}let B=j.field_name.map(G=>String.fromCharCode(G)).join("");B=B.split("\\")[0],(!B||B.length===0)&&(B="N/A");const q=D.field_data.field_model_names.map((G,W)=>{if(!D.field_models||D.field_models.length===0||W>=D.field_models.length)return null;const H=D.field_models[W],X=typeof G=="string"?G:Array.isArray(G)?String.fromCharCode(...G.filter(Je=>Je!==0)):"";return{name:(X.startsWith(B+"_")?X.substring(B.length+1):X).split(".")[0],x:H.x,y:H.y,z:H.z,direction:H.direction,triangle:H.triangle,collision:H.collision?0:1,interaction:H.interaction?0:1,visible:H.visible,lights:H.lights}}).filter(G=>G!==null),Ee=D.world_current_model;Ee.script=Ee.walkmesh_type>>5,Ee.walkmesh_type=Ee.walkmesh_type&31;const ve=k.field_battle_check===188649,Ze=k.field_battle_check===2425393296;let he=oo.Normal;ve?he=oo.Off:Ze&&(he=oo.Max);const V=D.world_models;let J=null;try{J=await T("read_chocobo_data")}catch(G){console.warn("Failed to load chocobo data:",G)}a(G=>({...G,currentModule:k.current_module,gameMoment:k.game_moment,fieldId:k.field_id,fieldCurrentModelId:k.field_current_model_id,fieldName:B,fieldFps:k.field_fps,battleFps:k.battle_fps,worldFps:k.world_fps,inGameTime:k.in_game_time,discId:k.disc_id,menuVisibility:k.menu_visibility,menuLocks:k.menu_locks,fieldMovementDisabled:k.field_movement_disabled,fieldMenuAccessEnabled:k.field_menu_access_enabled,fieldSkipDialoguesEnabled:k.field_skip_dialogues_check!==139,partyLockingBitmask:k.party_locking_mask,partyVisibilityBitmask:k.party_visibility_mask,gil:k.gil,gp:k.gp,battleCount:k.battle_count,battleEscapeCount:k.battle_escape_count,battlesDisabled:k.field_battle_check===188649,maxBattlesEnabled:k.field_battle_check===2425393296,randomEncounters:he,gameObjPtr:k.game_obj_ptr,battleSwirlDisabled:k.battle_swirl_check===0,instantATBEnabled:k.instant_atb_check===17863,manualSlotsEnabled:k.manual_slots_check===0,slotsActive:k.slots_active,speed:""+F,unfocusPatchEnabled:k.unfocus_patch_check===128,isFFnx:$,stepId:k.step_id,stepOffset:k.step_offset,stepFraction:k.step_fraction,dangerValue:k.danger_value,formationIndex:k.formation_idx,battleId:k.battle_id,fieldModels:q,battleAllies:D.battle_allies,battleEnemies:D.battle_enemies,invincibilityEnabled:k.invincibility_check!==20200,worldCurrentModel:Ee,expMultiplier:k.exp_multiplier!==56?k.exp_multiplier:1,gilMultiplier:k.gil_multiplier!==177?k.gil_multiplier:1,apMultiplier:k.ap_multiplier!==226?k.ap_multiplier:1,worldModels:V,battleChocoboRating:k.battle_chocobo_rating,menuAlwaysEnabled:k.menu_always_enabled===199,worldZoomTiltEnabled:k.world_zoom_tilt_enabled===1,worldZoom:k.world_zoom,worldTilt:k.world_tilt,worldSpeedMultiplier:k.world_speed_multiplier,partyMemberIds:k.party_member_ids,keyItems:E(k.key_items),partyMembers:D.party_members,zolomCoords:k.zolom_coords?[k.zolom_coords>>16,k.zolom_coords&65535]:null,worldMapType:k.world_map_type,fieldTmpVars:k.field_tmp_vars,fieldLines:D.field_lines,battleQueue:k.battle_queue,walkAnywhereEnabled:k.walk_anywhere_check===233,lovePoints:k.love_points,battlePoints:k.battle_points,chocoboData:J})),setTimeout(()=>{const G={};if(k.current_module===st.Battle){for(let W=0;W<4;W++){const H=D.battle_allies[W];R(H,W),H&&(G[W]=H.status)}for(let W=0;W<6;W++){const H=D.battle_enemies[W];R(H,W+4),H&&(G[W+4]=H.status)}f.current=G}else Object.keys(f.current).length>0&&(f.current={})},50),S.current=U,l(U!==st.None)}catch(D){console.warn("Could not read FF7 data: ",D),l(!1)}};O();const N=setInterval(O,125);return()=>clearInterval(N)},[t,n,r,i,s.currentModule]);const P=async()=>{if(i)try{h(!0);const E=await T("read_enemy_attack_names");d(O=>({...O,enemyAttackNames:E}))}catch(E){console.error("Failed to load enemy attack names:",E)}finally{h(!1)}};if(g.useEffect(()=>{i&&s.currentModule===st.Battle&&![0,65535].includes(s.battleId)&&P()},[i,s.currentModule,s.battleId]),n)return A.jsx("div",{className:"flex items-center justify-center h-screen",children:"Loading addresses..."});if(r)return A.jsxs("div",{className:"flex items-center justify-center h-screen text-red-500",children:["Error loading FF7 addresses: ",r.toString()]});if(!t)return A.jsx("div",{className:"flex items-center justify-center h-screen",children:"FF7 addresses not available. Is the game running?"});const z={connected:i,gameState:s,gameData:u,isLoadingGameData:m,hacks:y,setHacks:w,addresses:t,isLoadingAddresses:!1,errorAddresses:null};return A.jsx(lh.Provider,{value:z,children:e})},lw=()=>{const e=g.useContext(lh);if(e===void 0)throw new Error("useFF7Context must be used within a FF7Provider");return e};var xe;(function(e){e.WINDOW_RESIZED="tauri://resize",e.WINDOW_MOVED="tauri://move",e.WINDOW_CLOSE_REQUESTED="tauri://close-requested",e.WINDOW_DESTROYED="tauri://destroyed",e.WINDOW_FOCUS="tauri://focus",e.WINDOW_BLUR="tauri://blur",e.WINDOW_SCALE_FACTOR_CHANGED="tauri://scale-change",e.WINDOW_THEME_CHANGED="tauri://theme-changed",e.WINDOW_CREATED="tauri://window-created",e.WEBVIEW_CREATED="tauri://webview-created",e.DRAG_ENTER="tauri://drag-enter",e.DRAG_OVER="tauri://drag-over",e.DRAG_DROP="tauri://drag-drop",e.DRAG_LEAVE="tauri://drag-leave"})(xe||(xe={}));async function sh(e,t){await T("plugin:event|unlisten",{event:e,eventId:t})}async function _u(e,t,n){var r;const o=typeof(n==null?void 0:n.target)=="string"?{kind:"AnyLabel",label:n.target}:(r=n==null?void 0:n.target)!==null&&r!==void 0?r:{kind:"Any"};return T("plugin:event|listen",{event:e,target:o,handler:Qp(t)}).then(i=>async()=>sh(e,i))}async function ah(e,t,n){return _u(e,r=>{sh(e,r.id),t(r)},n)}async function uh(e,t){await T("plugin:event|emit",{event:e,payload:t})}async function ch(e,t,n){await T("plugin:event|emit_to",{target:typeof e=="string"?{kind:"AnyLabel",label:e}:e,event:t,payload:n})}function td(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function dh(...e){return t=>{let n=!1;const r=e.map(o=>{const i=td(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():td(e[o],null)}}}}function pe(...e){return g.useCallback(dh(...e),e)}function Eo(e){const t=aw(e),n=g.forwardRef((r,o)=>{const{children:i,...l}=r,s=g.Children.toArray(i),a=s.find(cw);if(a){const u=a.props.children,d=s.map(m=>m===a?g.Children.count(u)>1?g.Children.only(null):g.isValidElement(u)?u.props.children:null:m);return A.jsx(t,{...l,ref:o,children:g.isValidElement(u)?g.cloneElement(u,void 0,d):null})}return A.jsx(t,{...l,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}var sw=Eo("Slot");function aw(e){const t=g.forwardRef((n,r)=>{const{children:o,...i}=n;if(g.isValidElement(o)){const l=fw(o),s=dw(i,o.props);return o.type!==g.Fragment&&(s.ref=r?dh(r,l):l),g.cloneElement(o,s)}return g.Children.count(o)>1?g.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var fh=Symbol("radix.slottable");function uw(e){const t=({children:n})=>A.jsx(A.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=fh,t}function cw(e){return g.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===fh}function dw(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...s)=>{const a=i(...s);return o(...s),a}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function fw(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function ph(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=ph(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function hh(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=ph(e))&&(r&&(r+=" "),r+=t);return r}const nd=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,rd=hh,pw=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return rd(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,l=Object.keys(o).map(u=>{const d=n==null?void 0:n[u],m=i==null?void 0:i[u];if(d===null)return null;const h=nd(d)||nd(m);return o[u][h]}),s=n&&Object.entries(n).reduce((u,d)=>{let[m,h]=d;return h===void 0||(u[m]=h),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,d)=>{let{class:m,className:h,...y}=d;return Object.entries(y).every(w=>{let[f,S]=w;return Array.isArray(S)?S.includes({...i,...s}[f]):{...i,...s}[f]===S})?[...u,m,h]:u},[]);return rd(e,l,a,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hw=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),mh=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var mw={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gw=g.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:l,...s},a)=>g.createElement("svg",{ref:a,...mw,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:mh("lucide",o),...s},[...l.map(([u,d])=>g.createElement(u,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cu=(e,t)=>{const n=g.forwardRef(({className:r,...o},i)=>g.createElement(gw,{ref:i,iconNode:t,className:mh(`lucide-${hw(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vw=Cu("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gh=Cu("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yw=Cu("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Eu="-",ww=e=>{const t=xw(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:l=>{const s=l.split(Eu);return s[0]===""&&s.length!==1&&s.shift(),vh(s,t)||Sw(l)},getConflictingClassGroupIds:(l,s)=>{const a=n[l]||[];return s&&r[l]?[...a,...r[l]]:a}}},vh=(e,t)=>{var l;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?vh(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Eu);return(l=t.validators.find(({validator:s})=>s(i)))==null?void 0:l.classGroupId},od=/^\[(.+)\]$/,Sw=e=>{if(od.test(e)){const t=od.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},xw=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Cw(Object.entries(e.classGroups),n).forEach(([i,l])=>{ca(l,r,i,t)}),r},ca=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:id(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(_w(o)){ca(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,l])=>{ca(l,id(t,i),n,r)})})},id=(e,t)=>{let n=e;return t.split(Eu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},_w=e=>e.isThemeGetter,Cw=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([l,s])=>[t+l,s])):i);return[n,o]}):e,Ew=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,l)=>{n.set(i,l),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let l=n.get(i);if(l!==void 0)return l;if((l=r.get(i))!==void 0)return o(i,l),l},set(i,l){n.has(i)?n.set(i,l):o(i,l)}}},yh="!",kw=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,l=s=>{const a=[];let u=0,d=0,m;for(let S=0;S<s.length;S++){let p=s[S];if(u===0){if(p===o&&(r||s.slice(S,S+i)===t)){a.push(s.slice(d,S)),d=S+i;continue}if(p==="/"){m=S;continue}}p==="["?u++:p==="]"&&u--}const h=a.length===0?s:s.substring(d),y=h.startsWith(yh),w=y?h.substring(1):h,f=m&&m>d?m-d:void 0;return{modifiers:a,hasImportantModifier:y,baseClassName:w,maybePostfixModifierPosition:f}};return n?s=>n({className:s,parseClassName:l}):l},bw=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Pw=e=>({cache:Ew(e.cacheSize),parseClassName:kw(e),...ww(e)}),Rw=/\s+/,Nw=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],l=e.trim().split(Rw);let s="";for(let a=l.length-1;a>=0;a-=1){const u=l[a],{modifiers:d,hasImportantModifier:m,baseClassName:h,maybePostfixModifierPosition:y}=n(u);let w=!!y,f=r(w?h.substring(0,y):h);if(!f){if(!w){s=u+(s.length>0?" "+s:s);continue}if(f=r(h),!f){s=u+(s.length>0?" "+s:s);continue}w=!1}const S=bw(d).join(":"),p=m?S+yh:S,c=p+f;if(i.includes(c))continue;i.push(c);const v=o(f,w);for(let x=0;x<v.length;++x){const C=v[x];i.push(p+C)}s=u+(s.length>0?" "+s:s)}return s};function Aw(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=wh(t))&&(r&&(r+=" "),r+=n);return r}const wh=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=wh(e[r]))&&(n&&(n+=" "),n+=t);return n};function Tw(e,...t){let n,r,o,i=l;function l(a){const u=t.reduce((d,m)=>m(d),e());return n=Pw(u),r=n.cache.get,o=n.cache.set,i=s,s(a)}function s(a){const u=r(a);if(u)return u;const d=Nw(a,n);return o(a,d),d}return function(){return i(Aw.apply(null,arguments))}}const ne=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Sh=/^\[(?:([a-z-]+):)?(.+)\]$/i,Ow=/^\d+\/\d+$/,Lw=new Set(["px","full","screen"]),Dw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Iw=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,zw=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Mw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Fw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Rt=e=>ar(e)||Lw.has(e)||Ow.test(e),Vt=e=>kr(e,"length",Kw),ar=e=>!!e&&!Number.isNaN(Number(e)),os=e=>kr(e,"number",ar),Br=e=>!!e&&Number.isInteger(Number(e)),jw=e=>e.endsWith("%")&&ar(e.slice(0,-1)),K=e=>Sh.test(e),Ht=e=>Dw.test(e),Uw=new Set(["length","size","percentage"]),Bw=e=>kr(e,Uw,xh),$w=e=>kr(e,"position",xh),Vw=new Set(["image","url"]),Hw=e=>kr(e,Vw,Qw),Ww=e=>kr(e,"",Gw),$r=()=>!0,kr=(e,t,n)=>{const r=Sh.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Kw=e=>Iw.test(e)&&!zw.test(e),xh=()=>!1,Gw=e=>Mw.test(e),Qw=e=>Fw.test(e),Yw=()=>{const e=ne("colors"),t=ne("spacing"),n=ne("blur"),r=ne("brightness"),o=ne("borderColor"),i=ne("borderRadius"),l=ne("borderSpacing"),s=ne("borderWidth"),a=ne("contrast"),u=ne("grayscale"),d=ne("hueRotate"),m=ne("invert"),h=ne("gap"),y=ne("gradientColorStops"),w=ne("gradientColorStopPositions"),f=ne("inset"),S=ne("margin"),p=ne("opacity"),c=ne("padding"),v=ne("saturate"),x=ne("scale"),C=ne("sepia"),R=ne("skew"),_=ne("space"),P=ne("translate"),z=()=>["auto","contain","none"],E=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto",K,t],N=()=>[K,t],D=()=>["",Rt,Vt],k=()=>["auto",ar,K],U=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],j=()=>["solid","dashed","dotted","double","none"],$=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],b=()=>["start","end","center","between","around","evenly","stretch"],I=()=>["","0",K],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>[ar,K];return{cacheSize:500,separator:":",theme:{colors:[$r],spacing:[Rt,Vt],blur:["none","",Ht,K],brightness:B(),borderColor:[e],borderRadius:["none","","full",Ht,K],borderSpacing:N(),borderWidth:D(),contrast:B(),grayscale:I(),hueRotate:B(),invert:I(),gap:N(),gradientColorStops:[e],gradientColorStopPositions:[jw,Vt],inset:O(),margin:O(),opacity:B(),padding:N(),saturate:B(),scale:B(),sepia:I(),skew:B(),space:N(),translate:N()},classGroups:{aspect:[{aspect:["auto","square","video",K]}],container:["container"],columns:[{columns:[Ht]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...U(),K]}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Br,K]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",K]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",Br,K]}],"grid-cols":[{"grid-cols":[$r]}],"col-start-end":[{col:["auto",{span:["full",Br,K]},K]}],"col-start":[{"col-start":k()}],"col-end":[{"col-end":k()}],"grid-rows":[{"grid-rows":[$r]}],"row-start-end":[{row:["auto",{span:[Br,K]},K]}],"row-start":[{"row-start":k()}],"row-end":[{"row-end":k()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",K]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",K]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...b()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...b(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...b(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[c]}],px:[{px:[c]}],py:[{py:[c]}],ps:[{ps:[c]}],pe:[{pe:[c]}],pt:[{pt:[c]}],pr:[{pr:[c]}],pb:[{pb:[c]}],pl:[{pl:[c]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[_]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[_]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",K,t]}],"min-w":[{"min-w":[K,t,"min","max","fit"]}],"max-w":[{"max-w":[K,t,"none","full","min","max","fit","prose",{screen:[Ht]},Ht]}],h:[{h:[K,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[K,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[K,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[K,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ht,Vt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",os]}],"font-family":[{font:[$r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",K]}],"line-clamp":[{"line-clamp":["none",ar,os]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Rt,K]}],"list-image":[{"list-image":["none",K]}],"list-style-type":[{list:["none","disc","decimal",K]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[p]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[p]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...j(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Rt,Vt]}],"underline-offset":[{"underline-offset":["auto",Rt,K]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",K]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",K]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[p]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...U(),$w]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Bw]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Hw]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[p]}],"border-style":[{border:[...j(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[p]}],"divide-style":[{divide:j()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...j()]}],"outline-offset":[{"outline-offset":[Rt,K]}],"outline-w":[{outline:[Rt,Vt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:D()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[p]}],"ring-offset-w":[{"ring-offset":[Rt,Vt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ht,Ww]}],"shadow-color":[{shadow:[$r]}],opacity:[{opacity:[p]}],"mix-blend":[{"mix-blend":[...$(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":$()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",Ht,K]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[m]}],saturate:[{saturate:[v]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[m]}],"backdrop-opacity":[{"backdrop-opacity":[p]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",K]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",K]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",K]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[Br,K]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[R]}],"skew-y":[{"skew-y":[R]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",K]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",K]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",K]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Rt,Vt,os]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Xw=Tw(Yw);function kt(...e){return Xw(hh(e))}function Ux(e){const t={ARROWUP:"↑",ARROWDOWN:"↓",ARROWLEFT:"←",ARROWRIGHT:"→",ENTER:"⏎",ESCAPE:"Esc",BACKSPACE:"⌫",DELETE:"Del",SPACE:"Space",TAB:"⇥",CONTROL:"Ctrl",ALT:"Alt",SHIFT:"⇧",META:"⌘"};return e.includes("+")?e.split("+").map(n=>n==="Ctrl"||n==="Alt"||n==="Shift"?n:t[n]||n).join("+"):t[e]||e}const Zw=pw("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-95",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",xs:"h-4 rounded-md px-3 text-[9px] px-1 py-0",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),Jw=g.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{const l=r?sw:"button";return A.jsx(l,{className:kt(Zw({variant:t,size:n,className:e})),ref:i,...o})});Jw.displayName="Button";var qw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],ce=qw.reduce((e,t)=>{const n=Eo(`Primitive.${t}`),r=g.forwardRef((o,i)=>{const{asChild:l,...s}=o,a=l?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),A.jsx(a,{...s,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function e0(e,t){e&&Er.flushSync(()=>e.dispatchEvent(t))}function ee(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Bx(e,t){const n=g.createContext(t),r=i=>{const{children:l,...s}=i,a=g.useMemo(()=>s,Object.values(s));return A.jsx(n.Provider,{value:a,children:l})};r.displayName=e+"Provider";function o(i){const l=g.useContext(n);if(l)return l;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function gl(e,t=[]){let n=[];function r(i,l){const s=g.createContext(l),a=n.length;n=[...n,l];const u=m=>{var p;const{scope:h,children:y,...w}=m,f=((p=h==null?void 0:h[e])==null?void 0:p[a])||s,S=g.useMemo(()=>w,Object.values(w));return A.jsx(f.Provider,{value:S,children:y})};u.displayName=i+"Provider";function d(m,h){var f;const y=((f=h==null?void 0:h[e])==null?void 0:f[a])||s,w=g.useContext(y);if(w)return w;if(l!==void 0)return l;throw new Error(`\`${m}\` must be used within \`${i}\``)}return[u,d]}const o=()=>{const i=n.map(l=>g.createContext(l));return function(s){const a=(s==null?void 0:s[e])||i;return g.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return o.scopeName=e,[r,t0(o,...t)]}function t0(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const l=r.reduce((s,{useScope:a,scopeName:u})=>{const m=a(i)[`__scope${u}`];return{...s,...m}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}var Ne=globalThis!=null&&globalThis.document?g.useLayoutEffect:()=>{},n0=Hd[" useId ".trim().toString()]||(()=>{}),r0=0;function vl(e){const[t,n]=g.useState(n0());return Ne(()=>{e||n(r=>r??String(r0++))},[e]),e||(t?`radix-${t}`:"")}var o0=Hd[" useInsertionEffect ".trim().toString()]||Ne;function da({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,l]=i0({defaultProp:t,onChange:n}),s=e!==void 0,a=s?e:o;{const d=g.useRef(e!==void 0);g.useEffect(()=>{const m=d.current;m!==s&&console.warn(`${r} is changing from ${m?"controlled":"uncontrolled"} to ${s?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=s},[s,r])}const u=g.useCallback(d=>{var m;if(s){const h=l0(d)?d(e):d;h!==e&&((m=l.current)==null||m.call(l,h))}else i(d)},[s,e,i,l]);return[a,u]}function i0({defaultProp:e,onChange:t}){const[n,r]=g.useState(e),o=g.useRef(n),i=g.useRef(t);return o0(()=>{i.current=t},[t]),g.useEffect(()=>{var l;o.current!==n&&((l=i.current)==null||l.call(i,n),o.current=n)},[n,o]),[n,r,i]}function l0(e){return typeof e=="function"}function An(e){const t=g.useRef(e);return g.useEffect(()=>{t.current=e}),g.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function s0(e,t=globalThis==null?void 0:globalThis.document){const n=An(e);g.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var a0="DismissableLayer",fa="dismissableLayer.update",u0="dismissableLayer.pointerDownOutside",c0="dismissableLayer.focusOutside",ld,_h=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ku=g.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:l,onDismiss:s,...a}=e,u=g.useContext(_h),[d,m]=g.useState(null),h=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=g.useState({}),w=pe(t,_=>m(_)),f=Array.from(u.layers),[S]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),p=f.indexOf(S),c=d?f.indexOf(d):-1,v=u.layersWithOutsidePointerEventsDisabled.size>0,x=c>=p,C=p0(_=>{const P=_.target,z=[...u.branches].some(E=>E.contains(P));!x||z||(o==null||o(_),l==null||l(_),_.defaultPrevented||s==null||s())},h),R=h0(_=>{const P=_.target;[...u.branches].some(E=>E.contains(P))||(i==null||i(_),l==null||l(_),_.defaultPrevented||s==null||s())},h);return s0(_=>{c===u.layers.size-1&&(r==null||r(_),!_.defaultPrevented&&s&&(_.preventDefault(),s()))},h),g.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(ld=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),sd(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=ld)}},[d,h,n,u]),g.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),sd())},[d,u]),g.useEffect(()=>{const _=()=>y({});return document.addEventListener(fa,_),()=>document.removeEventListener(fa,_)},[]),A.jsx(ce.div,{...a,ref:w,style:{pointerEvents:v?x?"auto":"none":void 0,...e.style},onFocusCapture:ee(e.onFocusCapture,R.onFocusCapture),onBlurCapture:ee(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:ee(e.onPointerDownCapture,C.onPointerDownCapture)})});ku.displayName=a0;var d0="DismissableLayerBranch",f0=g.forwardRef((e,t)=>{const n=g.useContext(_h),r=g.useRef(null),o=pe(t,r);return g.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),A.jsx(ce.div,{...e,ref:o})});f0.displayName=d0;function p0(e,t=globalThis==null?void 0:globalThis.document){const n=An(e),r=g.useRef(!1),o=g.useRef(()=>{});return g.useEffect(()=>{const i=s=>{if(s.target&&!r.current){let a=function(){Ch(u0,n,u,{discrete:!0})};const u={originalEvent:s};s.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(l),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function h0(e,t=globalThis==null?void 0:globalThis.document){const n=An(e),r=g.useRef(!1);return g.useEffect(()=>{const o=i=>{i.target&&!r.current&&Ch(c0,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function sd(){const e=new CustomEvent(fa);document.dispatchEvent(e)}function Ch(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?e0(o,i):o.dispatchEvent(i)}var is="focusScope.autoFocusOnMount",ls="focusScope.autoFocusOnUnmount",ad={bubbles:!1,cancelable:!0},m0="FocusScope",Eh=g.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[s,a]=g.useState(null),u=An(o),d=An(i),m=g.useRef(null),h=pe(t,f=>a(f)),y=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(r){let f=function(v){if(y.paused||!s)return;const x=v.target;s.contains(x)?m.current=x:Wt(m.current,{select:!0})},S=function(v){if(y.paused||!s)return;const x=v.relatedTarget;x!==null&&(s.contains(x)||Wt(m.current,{select:!0}))},p=function(v){if(document.activeElement===document.body)for(const C of v)C.removedNodes.length>0&&Wt(s)};document.addEventListener("focusin",f),document.addEventListener("focusout",S);const c=new MutationObserver(p);return s&&c.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",f),document.removeEventListener("focusout",S),c.disconnect()}}},[r,s,y.paused]),g.useEffect(()=>{if(s){cd.add(y);const f=document.activeElement;if(!s.contains(f)){const p=new CustomEvent(is,ad);s.addEventListener(is,u),s.dispatchEvent(p),p.defaultPrevented||(g0(x0(kh(s)),{select:!0}),document.activeElement===f&&Wt(s))}return()=>{s.removeEventListener(is,u),setTimeout(()=>{const p=new CustomEvent(ls,ad);s.addEventListener(ls,d),s.dispatchEvent(p),p.defaultPrevented||Wt(f??document.body,{select:!0}),s.removeEventListener(ls,d),cd.remove(y)},0)}}},[s,u,d,y]);const w=g.useCallback(f=>{if(!n&&!r||y.paused)return;const S=f.key==="Tab"&&!f.altKey&&!f.ctrlKey&&!f.metaKey,p=document.activeElement;if(S&&p){const c=f.currentTarget,[v,x]=v0(c);v&&x?!f.shiftKey&&p===x?(f.preventDefault(),n&&Wt(v,{select:!0})):f.shiftKey&&p===v&&(f.preventDefault(),n&&Wt(x,{select:!0})):p===c&&f.preventDefault()}},[n,r,y.paused]);return A.jsx(ce.div,{tabIndex:-1,...l,ref:h,onKeyDown:w})});Eh.displayName=m0;function g0(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Wt(r,{select:t}),document.activeElement!==n)return}function v0(e){const t=kh(e),n=ud(t,e),r=ud(t.reverse(),e);return[n,r]}function kh(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ud(e,t){for(const n of e)if(!y0(n,{upTo:t}))return n}function y0(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function w0(e){return e instanceof HTMLInputElement&&"select"in e}function Wt(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&w0(e)&&t&&e.select()}}var cd=S0();function S0(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=dd(e,t),e.unshift(t)},remove(t){var n;e=dd(e,t),(n=e[0])==null||n.resume()}}}function dd(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function x0(e){return e.filter(t=>t.tagName!=="A")}var _0="Portal",bu=g.forwardRef((e,t)=>{var s;const{container:n,...r}=e,[o,i]=g.useState(!1);Ne(()=>i(!0),[]);const l=n||o&&((s=globalThis==null?void 0:globalThis.document)==null?void 0:s.body);return l?Ny.createPortal(A.jsx(ce.div,{...r,ref:t}),l):null});bu.displayName=_0;function C0(e,t){return g.useReducer((n,r)=>t[n][r]??n,e)}var Pu=e=>{const{present:t,children:n}=e,r=E0(t),o=typeof n=="function"?n({present:r.isPresent}):g.Children.only(n),i=pe(r.ref,k0(o));return typeof n=="function"||r.isPresent?g.cloneElement(o,{ref:i}):null};Pu.displayName="Presence";function E0(e){const[t,n]=g.useState(),r=g.useRef(null),o=g.useRef(e),i=g.useRef("none"),l=e?"mounted":"unmounted",[s,a]=C0(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const u=ei(r.current);i.current=s==="mounted"?u:"none"},[s]),Ne(()=>{const u=r.current,d=o.current;if(d!==e){const h=i.current,y=ei(u);e?a("MOUNT"):y==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(d&&h!==y?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),Ne(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,m=y=>{const f=ei(r.current).includes(y.animationName);if(y.target===t&&f&&(a("ANIMATION_END"),!o.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},h=y=>{y.target===t&&(i.current=ei(r.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",m),t.addEventListener("animationend",m),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",m),t.removeEventListener("animationend",m)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:g.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function ei(e){return(e==null?void 0:e.animationName)||"none"}function k0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var ss=0;function b0(){g.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??fd()),document.body.insertAdjacentElement("beforeend",e[1]??fd()),ss++,()=>{ss===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),ss--}},[])}function fd(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var xt=function(){return xt=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},xt.apply(this,arguments)};function bh(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function P0(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var xi="right-scroll-bar-position",_i="width-before-scroll-bar",R0="with-scroll-bars-hidden",N0="--removed-body-scroll-bar-size";function as(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function A0(e,t){var n=g.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var T0=typeof window<"u"?g.useLayoutEffect:g.useEffect,pd=new WeakMap;function O0(e,t){var n=A0(null,function(r){return e.forEach(function(o){return as(o,r)})});return T0(function(){var r=pd.get(n);if(r){var o=new Set(r),i=new Set(e),l=n.current;o.forEach(function(s){i.has(s)||as(s,null)}),i.forEach(function(s){o.has(s)||as(s,l)})}pd.set(n,e)},[e]),n}function L0(e){return e}function D0(e,t){t===void 0&&(t=L0);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var l=t(i,r);return n.push(l),function(){n=n.filter(function(s){return s!==l})}},assignSyncMedium:function(i){for(r=!0;n.length;){var l=n;n=[],l.forEach(i)}n={push:function(s){return i(s)},filter:function(){return n}}},assignMedium:function(i){r=!0;var l=[];if(n.length){var s=n;n=[],s.forEach(i),l=n}var a=function(){var d=l;l=[],d.forEach(i)},u=function(){return Promise.resolve().then(a)};u(),n={push:function(d){l.push(d),u()},filter:function(d){return l=l.filter(d),n}}}};return o}function I0(e){e===void 0&&(e={});var t=D0(null);return t.options=xt({async:!0,ssr:!1},e),t}var Ph=function(e){var t=e.sideCar,n=bh(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return g.createElement(r,xt({},n))};Ph.isSideCarExport=!0;function z0(e,t){return e.useMedium(t),Ph}var Rh=I0(),us=function(){},yl=g.forwardRef(function(e,t){var n=g.useRef(null),r=g.useState({onScrollCapture:us,onWheelCapture:us,onTouchMoveCapture:us}),o=r[0],i=r[1],l=e.forwardProps,s=e.children,a=e.className,u=e.removeScrollBar,d=e.enabled,m=e.shards,h=e.sideCar,y=e.noIsolation,w=e.inert,f=e.allowPinchZoom,S=e.as,p=S===void 0?"div":S,c=e.gapMode,v=bh(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=h,C=O0([n,t]),R=xt(xt({},v),o);return g.createElement(g.Fragment,null,d&&g.createElement(x,{sideCar:Rh,removeScrollBar:u,shards:m,noIsolation:y,inert:w,setCallbacks:i,allowPinchZoom:!!f,lockRef:n,gapMode:c}),l?g.cloneElement(g.Children.only(s),xt(xt({},R),{ref:C})):g.createElement(p,xt({},R,{className:a,ref:C}),s))});yl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};yl.classNames={fullWidth:_i,zeroRight:xi};var M0=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function F0(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=M0();return t&&e.setAttribute("nonce",t),e}function j0(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function U0(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var B0=function(){var e=0,t=null;return{add:function(n){e==0&&(t=F0())&&(j0(t,n),U0(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},$0=function(){var e=B0();return function(t,n){g.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Nh=function(){var e=$0(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},V0={left:0,top:0,right:0,gap:0},cs=function(e){return parseInt(e||"",10)||0},H0=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[cs(n),cs(r),cs(o)]},W0=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return V0;var t=H0(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},K0=Nh(),ur="data-scroll-locked",G0=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,s=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(R0,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(s,"px ").concat(r,`;
  }
  body[`).concat(ur,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(l,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(s,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(xi,` {
    right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat(_i,` {
    margin-right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat(xi," .").concat(xi,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(_i," .").concat(_i,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ur,`] {
    `).concat(N0,": ").concat(s,`px;
  }
`)},hd=function(){var e=parseInt(document.body.getAttribute(ur)||"0",10);return isFinite(e)?e:0},Q0=function(){g.useEffect(function(){return document.body.setAttribute(ur,(hd()+1).toString()),function(){var e=hd()-1;e<=0?document.body.removeAttribute(ur):document.body.setAttribute(ur,e.toString())}},[])},Y0=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Q0();var i=g.useMemo(function(){return W0(o)},[o]);return g.createElement(K0,{styles:G0(i,!t,o,n?"":"!important")})},pa=!1;if(typeof window<"u")try{var ti=Object.defineProperty({},"passive",{get:function(){return pa=!0,!0}});window.addEventListener("test",ti,ti),window.removeEventListener("test",ti,ti)}catch{pa=!1}var jn=pa?{passive:!1}:!1,X0=function(e){return e.tagName==="TEXTAREA"},Ah=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!X0(e)&&n[t]==="visible")},Z0=function(e){return Ah(e,"overflowY")},J0=function(e){return Ah(e,"overflowX")},md=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Th(e,r);if(o){var i=Oh(e,r),l=i[1],s=i[2];if(l>s)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},q0=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},e1=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Th=function(e,t){return e==="v"?Z0(t):J0(t)},Oh=function(e,t){return e==="v"?q0(t):e1(t)},t1=function(e,t){return e==="h"&&t==="rtl"?-1:1},n1=function(e,t,n,r,o){var i=t1(e,window.getComputedStyle(t).direction),l=i*r,s=n.target,a=t.contains(s),u=!1,d=l>0,m=0,h=0;do{var y=Oh(e,s),w=y[0],f=y[1],S=y[2],p=f-S-i*w;(w||p)&&Th(e,s)&&(m+=p,h+=w),s instanceof ShadowRoot?s=s.host:s=s.parentNode}while(!a&&s!==document.body||a&&(t.contains(s)||t===s));return(d&&(Math.abs(m)<1||!o)||!d&&(Math.abs(h)<1||!o))&&(u=!0),u},ni=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},gd=function(e){return[e.deltaX,e.deltaY]},vd=function(e){return e&&"current"in e?e.current:e},r1=function(e,t){return e[0]===t[0]&&e[1]===t[1]},o1=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},i1=0,Un=[];function l1(e){var t=g.useRef([]),n=g.useRef([0,0]),r=g.useRef(),o=g.useState(i1++)[0],i=g.useState(Nh)[0],l=g.useRef(e);g.useEffect(function(){l.current=e},[e]),g.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var f=P0([e.lockRef.current],(e.shards||[]).map(vd),!0).filter(Boolean);return f.forEach(function(S){return S.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),f.forEach(function(S){return S.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=g.useCallback(function(f,S){if("touches"in f&&f.touches.length===2||f.type==="wheel"&&f.ctrlKey)return!l.current.allowPinchZoom;var p=ni(f),c=n.current,v="deltaX"in f?f.deltaX:c[0]-p[0],x="deltaY"in f?f.deltaY:c[1]-p[1],C,R=f.target,_=Math.abs(v)>Math.abs(x)?"h":"v";if("touches"in f&&_==="h"&&R.type==="range")return!1;var P=md(_,R);if(!P)return!0;if(P?C=_:(C=_==="v"?"h":"v",P=md(_,R)),!P)return!1;if(!r.current&&"changedTouches"in f&&(v||x)&&(r.current=C),!C)return!0;var z=r.current||C;return n1(z,S,f,z==="h"?v:x,!0)},[]),a=g.useCallback(function(f){var S=f;if(!(!Un.length||Un[Un.length-1]!==i)){var p="deltaY"in S?gd(S):ni(S),c=t.current.filter(function(C){return C.name===S.type&&(C.target===S.target||S.target===C.shadowParent)&&r1(C.delta,p)})[0];if(c&&c.should){S.cancelable&&S.preventDefault();return}if(!c){var v=(l.current.shards||[]).map(vd).filter(Boolean).filter(function(C){return C.contains(S.target)}),x=v.length>0?s(S,v[0]):!l.current.noIsolation;x&&S.cancelable&&S.preventDefault()}}},[]),u=g.useCallback(function(f,S,p,c){var v={name:f,delta:S,target:p,should:c,shadowParent:s1(p)};t.current.push(v),setTimeout(function(){t.current=t.current.filter(function(x){return x!==v})},1)},[]),d=g.useCallback(function(f){n.current=ni(f),r.current=void 0},[]),m=g.useCallback(function(f){u(f.type,gd(f),f.target,s(f,e.lockRef.current))},[]),h=g.useCallback(function(f){u(f.type,ni(f),f.target,s(f,e.lockRef.current))},[]);g.useEffect(function(){return Un.push(i),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:h}),document.addEventListener("wheel",a,jn),document.addEventListener("touchmove",a,jn),document.addEventListener("touchstart",d,jn),function(){Un=Un.filter(function(f){return f!==i}),document.removeEventListener("wheel",a,jn),document.removeEventListener("touchmove",a,jn),document.removeEventListener("touchstart",d,jn)}},[]);var y=e.removeScrollBar,w=e.inert;return g.createElement(g.Fragment,null,w?g.createElement(i,{styles:o1(o)}):null,y?g.createElement(Y0,{gapMode:e.gapMode}):null)}function s1(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const a1=z0(Rh,l1);var Lh=g.forwardRef(function(e,t){return g.createElement(yl,xt({},e,{ref:t,sideCar:a1}))});Lh.classNames=yl.classNames;var u1=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Bn=new WeakMap,ri=new WeakMap,oi={},ds=0,Dh=function(e){return e&&(e.host||Dh(e.parentNode))},c1=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Dh(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},d1=function(e,t,n,r){var o=c1(t,Array.isArray(e)?e:[e]);oi[n]||(oi[n]=new WeakMap);var i=oi[n],l=[],s=new Set,a=new Set(o),u=function(m){!m||s.has(m)||(s.add(m),u(m.parentNode))};o.forEach(u);var d=function(m){!m||a.has(m)||Array.prototype.forEach.call(m.children,function(h){if(s.has(h))d(h);else try{var y=h.getAttribute(r),w=y!==null&&y!=="false",f=(Bn.get(h)||0)+1,S=(i.get(h)||0)+1;Bn.set(h,f),i.set(h,S),l.push(h),f===1&&w&&ri.set(h,!0),S===1&&h.setAttribute(n,"true"),w||h.setAttribute(r,"true")}catch(p){console.error("aria-hidden: cannot operate on ",h,p)}})};return d(t),s.clear(),ds++,function(){l.forEach(function(m){var h=Bn.get(m)-1,y=i.get(m)-1;Bn.set(m,h),i.set(m,y),h||(ri.has(m)||m.removeAttribute(r),ri.delete(m)),y||m.removeAttribute(n)}),ds--,ds||(Bn=new WeakMap,Bn=new WeakMap,ri=new WeakMap,oi={})}},f1=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=u1(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),d1(r,o,n,"aria-hidden")):function(){return null}};const p1=["top","right","bottom","left"],cn=Math.min,He=Math.max,Qi=Math.round,ii=Math.floor,Et=e=>({x:e,y:e}),h1={left:"right",right:"left",bottom:"top",top:"bottom"},m1={start:"end",end:"start"};function ha(e,t,n){return He(e,cn(t,n))}function jt(e,t){return typeof e=="function"?e(t):e}function Ut(e){return e.split("-")[0]}function br(e){return e.split("-")[1]}function Ru(e){return e==="x"?"y":"x"}function Nu(e){return e==="y"?"height":"width"}function dn(e){return["top","bottom"].includes(Ut(e))?"y":"x"}function Au(e){return Ru(dn(e))}function g1(e,t,n){n===void 0&&(n=!1);const r=br(e),o=Au(e),i=Nu(o);let l=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=Yi(l)),[l,Yi(l)]}function v1(e){const t=Yi(e);return[ma(e),t,ma(t)]}function ma(e){return e.replace(/start|end/g,t=>m1[t])}function y1(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],l=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:l;default:return[]}}function w1(e,t,n,r){const o=br(e);let i=y1(Ut(e),n==="start",r);return o&&(i=i.map(l=>l+"-"+o),t&&(i=i.concat(i.map(ma)))),i}function Yi(e){return e.replace(/left|right|bottom|top/g,t=>h1[t])}function S1(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ih(e){return typeof e!="number"?S1(e):{top:e,right:e,bottom:e,left:e}}function Xi(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function yd(e,t,n){let{reference:r,floating:o}=e;const i=dn(t),l=Au(t),s=Nu(l),a=Ut(t),u=i==="y",d=r.x+r.width/2-o.width/2,m=r.y+r.height/2-o.height/2,h=r[s]/2-o[s]/2;let y;switch(a){case"top":y={x:d,y:r.y-o.height};break;case"bottom":y={x:d,y:r.y+r.height};break;case"right":y={x:r.x+r.width,y:m};break;case"left":y={x:r.x-o.width,y:m};break;default:y={x:r.x,y:r.y}}switch(br(t)){case"start":y[l]-=h*(n&&u?-1:1);break;case"end":y[l]+=h*(n&&u?-1:1);break}return y}const x1=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,s=i.filter(Boolean),a=await(l.isRTL==null?void 0:l.isRTL(t));let u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:m}=yd(u,r,a),h=r,y={},w=0;for(let f=0;f<s.length;f++){const{name:S,fn:p}=s[f],{x:c,y:v,data:x,reset:C}=await p({x:d,y:m,initialPlacement:r,placement:h,strategy:o,middlewareData:y,rects:u,platform:l,elements:{reference:e,floating:t}});d=c??d,m=v??m,y={...y,[S]:{...y[S],...x}},C&&w<=50&&(w++,typeof C=="object"&&(C.placement&&(h=C.placement),C.rects&&(u=C.rects===!0?await l.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:d,y:m}=yd(u,h,a)),f=-1)}return{x:d,y:m,placement:h,strategy:o,middlewareData:y}};async function ko(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:l,elements:s,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:m="floating",altBoundary:h=!1,padding:y=0}=jt(t,e),w=Ih(y),S=s[h?m==="floating"?"reference":"floating":m],p=Xi(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(S)))==null||n?S:S.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:d,strategy:a})),c=m==="floating"?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,v=await(i.getOffsetParent==null?void 0:i.getOffsetParent(s.floating)),x=await(i.isElement==null?void 0:i.isElement(v))?await(i.getScale==null?void 0:i.getScale(v))||{x:1,y:1}:{x:1,y:1},C=Xi(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:c,offsetParent:v,strategy:a}):c);return{top:(p.top-C.top+w.top)/x.y,bottom:(C.bottom-p.bottom+w.bottom)/x.y,left:(p.left-C.left+w.left)/x.x,right:(C.right-p.right+w.right)/x.x}}const _1=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:l,elements:s,middlewareData:a}=t,{element:u,padding:d=0}=jt(e,t)||{};if(u==null)return{};const m=Ih(d),h={x:n,y:r},y=Au(o),w=Nu(y),f=await l.getDimensions(u),S=y==="y",p=S?"top":"left",c=S?"bottom":"right",v=S?"clientHeight":"clientWidth",x=i.reference[w]+i.reference[y]-h[y]-i.floating[w],C=h[y]-i.reference[y],R=await(l.getOffsetParent==null?void 0:l.getOffsetParent(u));let _=R?R[v]:0;(!_||!await(l.isElement==null?void 0:l.isElement(R)))&&(_=s.floating[v]||i.floating[w]);const P=x/2-C/2,z=_/2-f[w]/2-1,E=cn(m[p],z),O=cn(m[c],z),N=E,D=_-f[w]-O,k=_/2-f[w]/2+P,U=ha(N,k,D),j=!a.arrow&&br(o)!=null&&k!==U&&i.reference[w]/2-(k<N?E:O)-f[w]/2<0,$=j?k<N?k-N:k-D:0;return{[y]:h[y]+$,data:{[y]:U,centerOffset:k-U-$,...j&&{alignmentOffset:$}},reset:j}}}),C1=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:l,initialPlacement:s,platform:a,elements:u}=t,{mainAxis:d=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:f=!0,...S}=jt(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const p=Ut(o),c=dn(s),v=Ut(s)===s,x=await(a.isRTL==null?void 0:a.isRTL(u.floating)),C=h||(v||!f?[Yi(s)]:v1(s)),R=w!=="none";!h&&R&&C.push(...w1(s,f,w,x));const _=[s,...C],P=await ko(t,S),z=[];let E=((r=i.flip)==null?void 0:r.overflows)||[];if(d&&z.push(P[p]),m){const k=g1(o,l,x);z.push(P[k[0]],P[k[1]])}if(E=[...E,{placement:o,overflows:z}],!z.every(k=>k<=0)){var O,N;const k=(((O=i.flip)==null?void 0:O.index)||0)+1,U=_[k];if(U)return{data:{index:k,overflows:E},reset:{placement:U}};let j=(N=E.filter($=>$.overflows[0]<=0).sort(($,b)=>$.overflows[1]-b.overflows[1])[0])==null?void 0:N.placement;if(!j)switch(y){case"bestFit":{var D;const $=(D=E.filter(b=>{if(R){const I=dn(b.placement);return I===c||I==="y"}return!0}).map(b=>[b.placement,b.overflows.filter(I=>I>0).reduce((I,F)=>I+F,0)]).sort((b,I)=>b[1]-I[1])[0])==null?void 0:D[0];$&&(j=$);break}case"initialPlacement":j=s;break}if(o!==j)return{reset:{placement:j}}}return{}}}};function wd(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Sd(e){return p1.some(t=>e[t]>=0)}const E1=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=jt(e,t);switch(r){case"referenceHidden":{const i=await ko(t,{...o,elementContext:"reference"}),l=wd(i,n.reference);return{data:{referenceHiddenOffsets:l,referenceHidden:Sd(l)}}}case"escaped":{const i=await ko(t,{...o,altBoundary:!0}),l=wd(i,n.floating);return{data:{escapedOffsets:l,escaped:Sd(l)}}}default:return{}}}}};async function k1(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),l=Ut(n),s=br(n),a=dn(n)==="y",u=["left","top"].includes(l)?-1:1,d=i&&a?-1:1,m=jt(t,e);let{mainAxis:h,crossAxis:y,alignmentAxis:w}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return s&&typeof w=="number"&&(y=s==="end"?w*-1:w),a?{x:y*d,y:h*u}:{x:h*u,y:y*d}}const b1=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:l,middlewareData:s}=t,a=await k1(t,e);return l===((n=s.offset)==null?void 0:n.placement)&&(r=s.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:l}}}}},P1=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:s={fn:S=>{let{x:p,y:c}=S;return{x:p,y:c}}},...a}=jt(e,t),u={x:n,y:r},d=await ko(t,a),m=dn(Ut(o)),h=Ru(m);let y=u[h],w=u[m];if(i){const S=h==="y"?"top":"left",p=h==="y"?"bottom":"right",c=y+d[S],v=y-d[p];y=ha(c,y,v)}if(l){const S=m==="y"?"top":"left",p=m==="y"?"bottom":"right",c=w+d[S],v=w-d[p];w=ha(c,w,v)}const f=s.fn({...t,[h]:y,[m]:w});return{...f,data:{x:f.x-n,y:f.y-r,enabled:{[h]:i,[m]:l}}}}}},R1=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:s=0,mainAxis:a=!0,crossAxis:u=!0}=jt(e,t),d={x:n,y:r},m=dn(o),h=Ru(m);let y=d[h],w=d[m];const f=jt(s,t),S=typeof f=="number"?{mainAxis:f,crossAxis:0}:{mainAxis:0,crossAxis:0,...f};if(a){const v=h==="y"?"height":"width",x=i.reference[h]-i.floating[v]+S.mainAxis,C=i.reference[h]+i.reference[v]-S.mainAxis;y<x?y=x:y>C&&(y=C)}if(u){var p,c;const v=h==="y"?"width":"height",x=["top","left"].includes(Ut(o)),C=i.reference[m]-i.floating[v]+(x&&((p=l.offset)==null?void 0:p[m])||0)+(x?0:S.crossAxis),R=i.reference[m]+i.reference[v]+(x?0:((c=l.offset)==null?void 0:c[m])||0)-(x?S.crossAxis:0);w<C?w=C:w>R&&(w=R)}return{[h]:y,[m]:w}}}},N1=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:l,elements:s}=t,{apply:a=()=>{},...u}=jt(e,t),d=await ko(t,u),m=Ut(o),h=br(o),y=dn(o)==="y",{width:w,height:f}=i.floating;let S,p;m==="top"||m==="bottom"?(S=m,p=h===(await(l.isRTL==null?void 0:l.isRTL(s.floating))?"start":"end")?"left":"right"):(p=m,S=h==="end"?"top":"bottom");const c=f-d.top-d.bottom,v=w-d.left-d.right,x=cn(f-d[S],c),C=cn(w-d[p],v),R=!t.middlewareData.shift;let _=x,P=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=v),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(_=c),R&&!h){const E=He(d.left,0),O=He(d.right,0),N=He(d.top,0),D=He(d.bottom,0);y?P=w-2*(E!==0||O!==0?E+O:He(d.left,d.right)):_=f-2*(N!==0||D!==0?N+D:He(d.top,d.bottom))}await a({...t,availableWidth:P,availableHeight:_});const z=await l.getDimensions(s.floating);return w!==z.width||f!==z.height?{reset:{rects:!0}}:{}}}};function wl(){return typeof window<"u"}function Pr(e){return zh(e)?(e.nodeName||"").toLowerCase():"#document"}function Ge(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Pt(e){var t;return(t=(zh(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function zh(e){return wl()?e instanceof Node||e instanceof Ge(e).Node:!1}function mt(e){return wl()?e instanceof Element||e instanceof Ge(e).Element:!1}function bt(e){return wl()?e instanceof HTMLElement||e instanceof Ge(e).HTMLElement:!1}function xd(e){return!wl()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ge(e).ShadowRoot}function Lo(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=gt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function A1(e){return["table","td","th"].includes(Pr(e))}function Sl(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Tu(e){const t=Ou(),n=mt(e)?gt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function T1(e){let t=fn(e);for(;bt(t)&&!wr(t);){if(Tu(t))return t;if(Sl(t))return null;t=fn(t)}return null}function Ou(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function wr(e){return["html","body","#document"].includes(Pr(e))}function gt(e){return Ge(e).getComputedStyle(e)}function xl(e){return mt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function fn(e){if(Pr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||xd(e)&&e.host||Pt(e);return xd(t)?t.host:t}function Mh(e){const t=fn(e);return wr(t)?e.ownerDocument?e.ownerDocument.body:e.body:bt(t)&&Lo(t)?t:Mh(t)}function bo(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Mh(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),l=Ge(o);if(i){const s=ga(l);return t.concat(l,l.visualViewport||[],Lo(o)?o:[],s&&n?bo(s):[])}return t.concat(o,bo(o,[],n))}function ga(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Fh(e){const t=gt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=bt(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,s=Qi(n)!==i||Qi(r)!==l;return s&&(n=i,r=l),{width:n,height:r,$:s}}function Lu(e){return mt(e)?e:e.contextElement}function cr(e){const t=Lu(e);if(!bt(t))return Et(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Fh(t);let l=(i?Qi(n.width):n.width)/r,s=(i?Qi(n.height):n.height)/o;return(!l||!Number.isFinite(l))&&(l=1),(!s||!Number.isFinite(s))&&(s=1),{x:l,y:s}}const O1=Et(0);function jh(e){const t=Ge(e);return!Ou()||!t.visualViewport?O1:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function L1(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ge(e)?!1:t}function Tn(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=Lu(e);let l=Et(1);t&&(r?mt(r)&&(l=cr(r)):l=cr(e));const s=L1(i,n,r)?jh(i):Et(0);let a=(o.left+s.x)/l.x,u=(o.top+s.y)/l.y,d=o.width/l.x,m=o.height/l.y;if(i){const h=Ge(i),y=r&&mt(r)?Ge(r):r;let w=h,f=ga(w);for(;f&&r&&y!==w;){const S=cr(f),p=f.getBoundingClientRect(),c=gt(f),v=p.left+(f.clientLeft+parseFloat(c.paddingLeft))*S.x,x=p.top+(f.clientTop+parseFloat(c.paddingTop))*S.y;a*=S.x,u*=S.y,d*=S.x,m*=S.y,a+=v,u+=x,w=Ge(f),f=ga(w)}}return Xi({width:d,height:m,x:a,y:u})}function Du(e,t){const n=xl(e).scrollLeft;return t?t.left+n:Tn(Pt(e)).left+n}function Uh(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:Du(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function D1(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",l=Pt(r),s=t?Sl(t.floating):!1;if(r===l||s&&i)return n;let a={scrollLeft:0,scrollTop:0},u=Et(1);const d=Et(0),m=bt(r);if((m||!m&&!i)&&((Pr(r)!=="body"||Lo(l))&&(a=xl(r)),bt(r))){const y=Tn(r);u=cr(r),d.x=y.x+r.clientLeft,d.y=y.y+r.clientTop}const h=l&&!m&&!i?Uh(l,a,!0):Et(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+d.x+h.x,y:n.y*u.y-a.scrollTop*u.y+d.y+h.y}}function I1(e){return Array.from(e.getClientRects())}function z1(e){const t=Pt(e),n=xl(e),r=e.ownerDocument.body,o=He(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=He(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let l=-n.scrollLeft+Du(e);const s=-n.scrollTop;return gt(r).direction==="rtl"&&(l+=He(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:s}}function M1(e,t){const n=Ge(e),r=Pt(e),o=n.visualViewport;let i=r.clientWidth,l=r.clientHeight,s=0,a=0;if(o){i=o.width,l=o.height;const u=Ou();(!u||u&&t==="fixed")&&(s=o.offsetLeft,a=o.offsetTop)}return{width:i,height:l,x:s,y:a}}function F1(e,t){const n=Tn(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=bt(e)?cr(e):Et(1),l=e.clientWidth*i.x,s=e.clientHeight*i.y,a=o*i.x,u=r*i.y;return{width:l,height:s,x:a,y:u}}function _d(e,t,n){let r;if(t==="viewport")r=M1(e,n);else if(t==="document")r=z1(Pt(e));else if(mt(t))r=F1(t,n);else{const o=jh(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Xi(r)}function Bh(e,t){const n=fn(e);return n===t||!mt(n)||wr(n)?!1:gt(n).position==="fixed"||Bh(n,t)}function j1(e,t){const n=t.get(e);if(n)return n;let r=bo(e,[],!1).filter(s=>mt(s)&&Pr(s)!=="body"),o=null;const i=gt(e).position==="fixed";let l=i?fn(e):e;for(;mt(l)&&!wr(l);){const s=gt(l),a=Tu(l);!a&&s.position==="fixed"&&(o=null),(i?!a&&!o:!a&&s.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Lo(l)&&!a&&Bh(e,l))?r=r.filter(d=>d!==l):o=s,l=fn(l)}return t.set(e,r),r}function U1(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const l=[...n==="clippingAncestors"?Sl(t)?[]:j1(t,this._c):[].concat(n),r],s=l[0],a=l.reduce((u,d)=>{const m=_d(t,d,o);return u.top=He(m.top,u.top),u.right=cn(m.right,u.right),u.bottom=cn(m.bottom,u.bottom),u.left=He(m.left,u.left),u},_d(t,s,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function B1(e){const{width:t,height:n}=Fh(e);return{width:t,height:n}}function $1(e,t,n){const r=bt(t),o=Pt(t),i=n==="fixed",l=Tn(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const a=Et(0);if(r||!r&&!i)if((Pr(t)!=="body"||Lo(o))&&(s=xl(t)),r){const h=Tn(t,!0,i,t);a.x=h.x+t.clientLeft,a.y=h.y+t.clientTop}else o&&(a.x=Du(o));const u=o&&!r&&!i?Uh(o,s):Et(0),d=l.left+s.scrollLeft-a.x-u.x,m=l.top+s.scrollTop-a.y-u.y;return{x:d,y:m,width:l.width,height:l.height}}function fs(e){return gt(e).position==="static"}function Cd(e,t){if(!bt(e)||gt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Pt(e)===n&&(n=n.ownerDocument.body),n}function $h(e,t){const n=Ge(e);if(Sl(e))return n;if(!bt(e)){let o=fn(e);for(;o&&!wr(o);){if(mt(o)&&!fs(o))return o;o=fn(o)}return n}let r=Cd(e,t);for(;r&&A1(r)&&fs(r);)r=Cd(r,t);return r&&wr(r)&&fs(r)&&!Tu(r)?n:r||T1(e)||n}const V1=async function(e){const t=this.getOffsetParent||$h,n=this.getDimensions,r=await n(e.floating);return{reference:$1(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function H1(e){return gt(e).direction==="rtl"}const W1={convertOffsetParentRelativeRectToViewportRelativeRect:D1,getDocumentElement:Pt,getClippingRect:U1,getOffsetParent:$h,getElementRects:V1,getClientRects:I1,getDimensions:B1,getScale:cr,isElement:mt,isRTL:H1};function K1(e,t){let n=null,r;const o=Pt(e);function i(){var s;clearTimeout(r),(s=n)==null||s.disconnect(),n=null}function l(s,a){s===void 0&&(s=!1),a===void 0&&(a=1),i();const{left:u,top:d,width:m,height:h}=e.getBoundingClientRect();if(s||t(),!m||!h)return;const y=ii(d),w=ii(o.clientWidth-(u+m)),f=ii(o.clientHeight-(d+h)),S=ii(u),c={rootMargin:-y+"px "+-w+"px "+-f+"px "+-S+"px",threshold:He(0,cn(1,a))||1};let v=!0;function x(C){const R=C[0].intersectionRatio;if(R!==a){if(!v)return l();R?l(!1,R):r=setTimeout(()=>{l(!1,1e-7)},1e3)}v=!1}try{n=new IntersectionObserver(x,{...c,root:o.ownerDocument})}catch{n=new IntersectionObserver(x,c)}n.observe(e)}return l(!0),i}function G1(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:l=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=Lu(e),d=o||i?[...u?bo(u):[],...bo(t)]:[];d.forEach(p=>{o&&p.addEventListener("scroll",n,{passive:!0}),i&&p.addEventListener("resize",n)});const m=u&&s?K1(u,n):null;let h=-1,y=null;l&&(y=new ResizeObserver(p=>{let[c]=p;c&&c.target===u&&y&&(y.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var v;(v=y)==null||v.observe(t)})),n()}),u&&!a&&y.observe(u),y.observe(t));let w,f=a?Tn(e):null;a&&S();function S(){const p=Tn(e);f&&(p.x!==f.x||p.y!==f.y||p.width!==f.width||p.height!==f.height)&&n(),f=p,w=requestAnimationFrame(S)}return n(),()=>{var p;d.forEach(c=>{o&&c.removeEventListener("scroll",n),i&&c.removeEventListener("resize",n)}),m==null||m(),(p=y)==null||p.disconnect(),y=null,a&&cancelAnimationFrame(w)}}const Q1=b1,Y1=P1,X1=C1,Z1=N1,J1=E1,Ed=_1,q1=R1,eS=(e,t,n)=>{const r=new Map,o={platform:W1,...n},i={...o.platform,_c:r};return x1(e,t,{...o,platform:i})};var Ci=typeof document<"u"?g.useLayoutEffect:g.useEffect;function Zi(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Zi(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Zi(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Vh(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function kd(e,t){const n=Vh(e);return Math.round(t*n)/n}function ps(e){const t=g.useRef(e);return Ci(()=>{t.current=e}),t}function tS(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:s=!0,whileElementsMounted:a,open:u}=e,[d,m]=g.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,y]=g.useState(r);Zi(h,r)||y(r);const[w,f]=g.useState(null),[S,p]=g.useState(null),c=g.useCallback(b=>{b!==R.current&&(R.current=b,f(b))},[]),v=g.useCallback(b=>{b!==_.current&&(_.current=b,p(b))},[]),x=i||w,C=l||S,R=g.useRef(null),_=g.useRef(null),P=g.useRef(d),z=a!=null,E=ps(a),O=ps(o),N=ps(u),D=g.useCallback(()=>{if(!R.current||!_.current)return;const b={placement:t,strategy:n,middleware:h};O.current&&(b.platform=O.current),eS(R.current,_.current,b).then(I=>{const F={...I,isPositioned:N.current!==!1};k.current&&!Zi(P.current,F)&&(P.current=F,Er.flushSync(()=>{m(F)}))})},[h,t,n,O,N]);Ci(()=>{u===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,m(b=>({...b,isPositioned:!1})))},[u]);const k=g.useRef(!1);Ci(()=>(k.current=!0,()=>{k.current=!1}),[]),Ci(()=>{if(x&&(R.current=x),C&&(_.current=C),x&&C){if(E.current)return E.current(x,C,D);D()}},[x,C,D,E,z]);const U=g.useMemo(()=>({reference:R,floating:_,setReference:c,setFloating:v}),[c,v]),j=g.useMemo(()=>({reference:x,floating:C}),[x,C]),$=g.useMemo(()=>{const b={position:n,left:0,top:0};if(!j.floating)return b;const I=kd(j.floating,d.x),F=kd(j.floating,d.y);return s?{...b,transform:"translate("+I+"px, "+F+"px)",...Vh(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:I,top:F}},[n,s,j.floating,d.x,d.y]);return g.useMemo(()=>({...d,update:D,refs:U,elements:j,floatingStyles:$}),[d,D,U,j,$])}const nS=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Ed({element:r.current,padding:o}).fn(n):{}:r?Ed({element:r,padding:o}).fn(n):{}}}},rS=(e,t)=>({...Q1(e),options:[e,t]}),oS=(e,t)=>({...Y1(e),options:[e,t]}),iS=(e,t)=>({...q1(e),options:[e,t]}),lS=(e,t)=>({...X1(e),options:[e,t]}),sS=(e,t)=>({...Z1(e),options:[e,t]}),aS=(e,t)=>({...J1(e),options:[e,t]}),uS=(e,t)=>({...nS(e),options:[e,t]});var cS="Arrow",Hh=g.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return A.jsx(ce.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:A.jsx("polygon",{points:"0,0 30,0 15,10"})})});Hh.displayName=cS;var dS=Hh;function fS(e){const[t,n]=g.useState(void 0);return Ne(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let l,s;if("borderBoxSize"in i){const a=i.borderBoxSize,u=Array.isArray(a)?a[0]:a;l=u.inlineSize,s=u.blockSize}else l=e.offsetWidth,s=e.offsetHeight;n({width:l,height:s})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Iu="Popper",[Wh,_l]=gl(Iu),[pS,Kh]=Wh(Iu),Gh=e=>{const{__scopePopper:t,children:n}=e,[r,o]=g.useState(null);return A.jsx(pS,{scope:t,anchor:r,onAnchorChange:o,children:n})};Gh.displayName=Iu;var Qh="PopperAnchor",Yh=g.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Kh(Qh,n),l=g.useRef(null),s=pe(t,l);return g.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||l.current)}),r?null:A.jsx(ce.div,{...o,ref:s})});Yh.displayName=Qh;var zu="PopperContent",[hS,mS]=Wh(zu),Xh=g.forwardRef((e,t)=>{var V,J,G,W,H,X;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:l=0,arrowPadding:s=0,avoidCollisions:a=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:m="partial",hideWhenDetached:h=!1,updatePositionStrategy:y="optimized",onPlaced:w,...f}=e,S=Kh(zu,n),[p,c]=g.useState(null),v=pe(t,De=>c(De)),[x,C]=g.useState(null),R=fS(x),_=(R==null?void 0:R.width)??0,P=(R==null?void 0:R.height)??0,z=r+(i!=="center"?"-"+i:""),E=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},O=Array.isArray(u)?u:[u],N=O.length>0,D={padding:E,boundary:O.filter(vS),altBoundary:N},{refs:k,floatingStyles:U,placement:j,isPositioned:$,middlewareData:b}=tS({strategy:"fixed",placement:z,whileElementsMounted:(...De)=>G1(...De,{animationFrame:y==="always"}),elements:{reference:S.anchor},middleware:[rS({mainAxis:o+P,alignmentAxis:l}),a&&oS({mainAxis:!0,crossAxis:!1,limiter:m==="partial"?iS():void 0,...D}),a&&lS({...D}),sS({...D,apply:({elements:De,rects:Je,availableWidth:Nr,availableHeight:Ar})=>{const{width:Tr,height:ng}=Je.reference,Io=De.floating.style;Io.setProperty("--radix-popper-available-width",`${Nr}px`),Io.setProperty("--radix-popper-available-height",`${Ar}px`),Io.setProperty("--radix-popper-anchor-width",`${Tr}px`),Io.setProperty("--radix-popper-anchor-height",`${ng}px`)}}),x&&uS({element:x,padding:s}),yS({arrowWidth:_,arrowHeight:P}),h&&aS({strategy:"referenceHidden",...D})]}),[I,F]=qh(j),B=An(w);Ne(()=>{$&&(B==null||B())},[$,B]);const q=(V=b.arrow)==null?void 0:V.x,Ee=(J=b.arrow)==null?void 0:J.y,ve=((G=b.arrow)==null?void 0:G.centerOffset)!==0,[Ze,he]=g.useState();return Ne(()=>{p&&he(window.getComputedStyle(p).zIndex)},[p]),A.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:$?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ze,"--radix-popper-transform-origin":[(W=b.transformOrigin)==null?void 0:W.x,(H=b.transformOrigin)==null?void 0:H.y].join(" "),...((X=b.hide)==null?void 0:X.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:A.jsx(hS,{scope:n,placedSide:I,onArrowChange:C,arrowX:q,arrowY:Ee,shouldHideArrow:ve,children:A.jsx(ce.div,{"data-side":I,"data-align":F,...f,ref:v,style:{...f.style,animation:$?void 0:"none"}})})})});Xh.displayName=zu;var Zh="PopperArrow",gS={top:"bottom",right:"left",bottom:"top",left:"right"},Jh=g.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=mS(Zh,r),l=gS[i.placedSide];return A.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:A.jsx(dS,{...o,ref:n,style:{...o.style,display:"block"}})})});Jh.displayName=Zh;function vS(e){return e!==null}var yS=e=>({name:"transformOrigin",options:e,fn(t){var S,p,c;const{placement:n,rects:r,middlewareData:o}=t,l=((S=o.arrow)==null?void 0:S.centerOffset)!==0,s=l?0:e.arrowWidth,a=l?0:e.arrowHeight,[u,d]=qh(n),m={start:"0%",center:"50%",end:"100%"}[d],h=(((p=o.arrow)==null?void 0:p.x)??0)+s/2,y=(((c=o.arrow)==null?void 0:c.y)??0)+a/2;let w="",f="";return u==="bottom"?(w=l?m:`${h}px`,f=`${-a}px`):u==="top"?(w=l?m:`${h}px`,f=`${r.floating.height+a}px`):u==="right"?(w=`${-a}px`,f=l?m:`${y}px`):u==="left"&&(w=`${r.floating.width+a}px`,f=l?m:`${y}px`),{data:{x:w,y:f}}}});function qh(e){const[t,n="center"]=e.split("-");return[t,n]}var em=Gh,tm=Yh,nm=Xh,rm=Jh,om=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),wS="VisuallyHidden",im=g.forwardRef((e,t)=>A.jsx(ce.span,{...e,ref:t,style:{...om,...e.style}}));im.displayName=wS;var SS=im,[Cl,$x]=gl("Tooltip",[_l]),El=_l(),lm="TooltipProvider",xS=700,va="tooltip.open",[_S,Mu]=Cl(lm),sm=e=>{const{__scopeTooltip:t,delayDuration:n=xS,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,l=g.useRef(!0),s=g.useRef(!1),a=g.useRef(0);return g.useEffect(()=>{const u=a.current;return()=>window.clearTimeout(u)},[]),A.jsx(_S,{scope:t,isOpenDelayedRef:l,delayDuration:n,onOpen:g.useCallback(()=>{window.clearTimeout(a.current),l.current=!1},[]),onClose:g.useCallback(()=>{window.clearTimeout(a.current),a.current=window.setTimeout(()=>l.current=!0,r)},[r]),isPointerInTransitRef:s,onPointerInTransitChange:g.useCallback(u=>{s.current=u},[]),disableHoverableContent:o,children:i})};sm.displayName=lm;var Po="Tooltip",[CS,Do]=Cl(Po),am=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o,onOpenChange:i,disableHoverableContent:l,delayDuration:s}=e,a=Mu(Po,e.__scopeTooltip),u=El(t),[d,m]=g.useState(null),h=vl(),y=g.useRef(0),w=l??a.disableHoverableContent,f=s??a.delayDuration,S=g.useRef(!1),[p,c]=da({prop:r,defaultProp:o??!1,onChange:_=>{_?(a.onOpen(),document.dispatchEvent(new CustomEvent(va))):a.onClose(),i==null||i(_)},caller:Po}),v=g.useMemo(()=>p?S.current?"delayed-open":"instant-open":"closed",[p]),x=g.useCallback(()=>{window.clearTimeout(y.current),y.current=0,S.current=!1,c(!0)},[c]),C=g.useCallback(()=>{window.clearTimeout(y.current),y.current=0,c(!1)},[c]),R=g.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{S.current=!0,c(!0),y.current=0},f)},[f,c]);return g.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),A.jsx(em,{...u,children:A.jsx(CS,{scope:t,contentId:h,open:p,stateAttribute:v,trigger:d,onTriggerChange:m,onTriggerEnter:g.useCallback(()=>{a.isOpenDelayedRef.current?R():x()},[a.isOpenDelayedRef,R,x]),onTriggerLeave:g.useCallback(()=>{w?C():(window.clearTimeout(y.current),y.current=0)},[C,w]),onOpen:x,onClose:C,disableHoverableContent:w,children:n})})};am.displayName=Po;var ya="TooltipTrigger",um=g.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Do(ya,n),i=Mu(ya,n),l=El(n),s=g.useRef(null),a=pe(t,s,o.onTriggerChange),u=g.useRef(!1),d=g.useRef(!1),m=g.useCallback(()=>u.current=!1,[]);return g.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),A.jsx(tm,{asChild:!0,...l,children:A.jsx(ce.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:a,onPointerMove:ee(e.onPointerMove,h=>{h.pointerType!=="touch"&&!d.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:ee(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:ee(e.onPointerDown,()=>{o.open&&o.onClose(),u.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:ee(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ee(e.onBlur,o.onClose),onClick:ee(e.onClick,o.onClose)})})});um.displayName=ya;var Fu="TooltipPortal",[ES,kS]=Cl(Fu,{forceMount:void 0}),cm=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=Do(Fu,t);return A.jsx(ES,{scope:t,forceMount:n,children:A.jsx(Pu,{present:n||i.open,children:A.jsx(bu,{asChild:!0,container:o,children:r})})})};cm.displayName=Fu;var Sr="TooltipContent",dm=g.forwardRef((e,t)=>{const n=kS(Sr,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=Do(Sr,e.__scopeTooltip);return A.jsx(Pu,{present:r||l.open,children:l.disableHoverableContent?A.jsx(fm,{side:o,...i,ref:t}):A.jsx(bS,{side:o,...i,ref:t})})}),bS=g.forwardRef((e,t)=>{const n=Do(Sr,e.__scopeTooltip),r=Mu(Sr,e.__scopeTooltip),o=g.useRef(null),i=pe(t,o),[l,s]=g.useState(null),{trigger:a,onClose:u}=n,d=o.current,{onPointerInTransitChange:m}=r,h=g.useCallback(()=>{s(null),m(!1)},[m]),y=g.useCallback((w,f)=>{const S=w.currentTarget,p={x:w.clientX,y:w.clientY},c=TS(p,S.getBoundingClientRect()),v=OS(p,c),x=LS(f.getBoundingClientRect()),C=IS([...v,...x]);s(C),m(!0)},[m]);return g.useEffect(()=>()=>h(),[h]),g.useEffect(()=>{if(a&&d){const w=S=>y(S,d),f=S=>y(S,a);return a.addEventListener("pointerleave",w),d.addEventListener("pointerleave",f),()=>{a.removeEventListener("pointerleave",w),d.removeEventListener("pointerleave",f)}}},[a,d,y,h]),g.useEffect(()=>{if(l){const w=f=>{const S=f.target,p={x:f.clientX,y:f.clientY},c=(a==null?void 0:a.contains(S))||(d==null?void 0:d.contains(S)),v=!DS(p,l);c?h():v&&(h(),u())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[a,d,l,u,h]),A.jsx(fm,{...e,ref:i})}),[PS,RS]=Cl(Po,{isInside:!1}),NS=uw("TooltipContent"),fm=g.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:l,...s}=e,a=Do(Sr,n),u=El(n),{onClose:d}=a;return g.useEffect(()=>(document.addEventListener(va,d),()=>document.removeEventListener(va,d)),[d]),g.useEffect(()=>{if(a.trigger){const m=h=>{const y=h.target;y!=null&&y.contains(a.trigger)&&d()};return window.addEventListener("scroll",m,{capture:!0}),()=>window.removeEventListener("scroll",m,{capture:!0})}},[a.trigger,d]),A.jsx(ku,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:m=>m.preventDefault(),onDismiss:d,children:A.jsxs(nm,{"data-state":a.stateAttribute,...u,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[A.jsx(NS,{children:r}),A.jsx(PS,{scope:n,isInside:!0,children:A.jsx(SS,{id:a.contentId,role:"tooltip",children:o||r})})]})})});dm.displayName=Sr;var pm="TooltipArrow",AS=g.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=El(n);return RS(pm,n).isInside?null:A.jsx(rm,{...o,...r,ref:t})});AS.displayName=pm;function TS(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function OS(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function LS(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function DS(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,l=t.length-1;i<t.length;l=i++){const s=t[i],a=t[l],u=s.x,d=s.y,m=a.x,h=a.y;d>r!=h>r&&n<(m-u)*(r-d)/(h-d)+u&&(o=!o)}return o}function IS(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),zS(t)}function zS(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],l=t[t.length-2];if((i.x-l.x)*(o.y-l.y)>=(i.y-l.y)*(o.x-l.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],l=n[n.length-2];if((i.x-l.x)*(o.y-l.y)>=(i.y-l.y)*(o.x-l.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var MS=sm,FS=am,jS=um,US=cm,hm=dm;const Vx=MS,Hx=({delayDuration:e=500,...t})=>A.jsx(FS,{delayDuration:e,...t}),Wx=jS,BS=g.forwardRef(({className:e,sideOffset:t=4,...n},r)=>A.jsx(US,{children:A.jsx(hm,{ref:r,sideOffset:t,className:kt("z-50 overflow-hidden rounded-md bg-zinc-900 px-3 py-1.5 text-xs text-zinc-100 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));BS.displayName=hm.displayName;function $S(e){const t=e+"CollectionProvider",[n,r]=gl(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=f=>{const{scope:S,children:p}=f,c=Nt.useRef(null),v=Nt.useRef(new Map).current;return A.jsx(o,{scope:S,itemMap:v,collectionRef:c,children:p})};l.displayName=t;const s=e+"CollectionSlot",a=Eo(s),u=Nt.forwardRef((f,S)=>{const{scope:p,children:c}=f,v=i(s,p),x=pe(S,v.collectionRef);return A.jsx(a,{ref:x,children:c})});u.displayName=s;const d=e+"CollectionItemSlot",m="data-radix-collection-item",h=Eo(d),y=Nt.forwardRef((f,S)=>{const{scope:p,children:c,...v}=f,x=Nt.useRef(null),C=pe(S,x),R=i(d,p);return Nt.useEffect(()=>(R.itemMap.set(x,{ref:x,...v}),()=>void R.itemMap.delete(x))),A.jsx(h,{[m]:"",ref:C,children:c})});y.displayName=d;function w(f){const S=i(e+"CollectionConsumer",f);return Nt.useCallback(()=>{const c=S.collectionRef.current;if(!c)return[];const v=Array.from(c.querySelectorAll(`[${m}]`));return Array.from(S.itemMap.values()).sort((R,_)=>v.indexOf(R.ref.current)-v.indexOf(_.ref.current))},[S.collectionRef,S.itemMap])}return[{Provider:l,Slot:u,ItemSlot:y},w,r]}var VS=g.createContext(void 0);function HS(e){const t=g.useContext(VS);return e||t||"ltr"}function WS(e){const t=g.useRef({value:e,previous:e});return g.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function bd(e,[t,n]){return Math.min(n,Math.max(t,e))}var KS=[" ","Enter","ArrowUp","ArrowDown"],GS=[" ","Enter"],On="Select",[kl,bl,QS]=$S(On),[Rr,Kx]=gl(On,[QS,_l]),Pl=_l(),[YS,gn]=Rr(On),[XS,ZS]=Rr(On),mm=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:l,defaultValue:s,onValueChange:a,dir:u,name:d,autoComplete:m,disabled:h,required:y,form:w}=e,f=Pl(t),[S,p]=g.useState(null),[c,v]=g.useState(null),[x,C]=g.useState(!1),R=HS(u),[_,P]=da({prop:r,defaultProp:o??!1,onChange:i,caller:On}),[z,E]=da({prop:l,defaultProp:s,onChange:a,caller:On}),O=g.useRef(null),N=S?w||!!S.closest("form"):!0,[D,k]=g.useState(new Set),U=Array.from(D).map(j=>j.props.value).join(";");return A.jsx(em,{...f,children:A.jsxs(YS,{required:y,scope:t,trigger:S,onTriggerChange:p,valueNode:c,onValueNodeChange:v,valueNodeHasChildren:x,onValueNodeHasChildrenChange:C,contentId:vl(),value:z,onValueChange:E,open:_,onOpenChange:P,dir:R,triggerPointerDownPosRef:O,disabled:h,children:[A.jsx(kl.Provider,{scope:t,children:A.jsx(XS,{scope:e.__scopeSelect,onNativeOptionAdd:g.useCallback(j=>{k($=>new Set($).add(j))},[]),onNativeOptionRemove:g.useCallback(j=>{k($=>{const b=new Set($);return b.delete(j),b})},[]),children:n})}),N?A.jsxs(jm,{"aria-hidden":!0,required:y,tabIndex:-1,name:d,autoComplete:m,value:z,onChange:j=>E(j.target.value),disabled:h,form:w,children:[z===void 0?A.jsx("option",{value:""}):null,Array.from(D)]},U):null]})})};mm.displayName=On;var gm="SelectTrigger",vm=g.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,i=Pl(n),l=gn(gm,n),s=l.disabled||r,a=pe(t,l.onTriggerChange),u=bl(n),d=g.useRef("touch"),[m,h,y]=Bm(f=>{const S=u().filter(v=>!v.disabled),p=S.find(v=>v.value===l.value),c=$m(S,f,p);c!==void 0&&l.onValueChange(c.value)}),w=f=>{s||(l.onOpenChange(!0),y()),f&&(l.triggerPointerDownPosRef.current={x:Math.round(f.pageX),y:Math.round(f.pageY)})};return A.jsx(tm,{asChild:!0,...i,children:A.jsx(ce.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":Um(l.value)?"":void 0,...o,ref:a,onClick:ee(o.onClick,f=>{f.currentTarget.focus(),d.current!=="mouse"&&w(f)}),onPointerDown:ee(o.onPointerDown,f=>{d.current=f.pointerType;const S=f.target;S.hasPointerCapture(f.pointerId)&&S.releasePointerCapture(f.pointerId),f.button===0&&f.ctrlKey===!1&&f.pointerType==="mouse"&&(w(f),f.preventDefault())}),onKeyDown:ee(o.onKeyDown,f=>{const S=m.current!=="";!(f.ctrlKey||f.altKey||f.metaKey)&&f.key.length===1&&h(f.key),!(S&&f.key===" ")&&KS.includes(f.key)&&(w(),f.preventDefault())})})})});vm.displayName=gm;var ym="SelectValue",wm=g.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...s}=e,a=gn(ym,n),{onValueNodeHasChildrenChange:u}=a,d=i!==void 0,m=pe(t,a.onValueNodeChange);return Ne(()=>{u(d)},[u,d]),A.jsx(ce.span,{...s,ref:m,style:{pointerEvents:"none"},children:Um(a.value)?A.jsx(A.Fragment,{children:l}):i})});wm.displayName=ym;var JS="SelectIcon",Sm=g.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return A.jsx(ce.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});Sm.displayName=JS;var qS="SelectPortal",xm=e=>A.jsx(bu,{asChild:!0,...e});xm.displayName=qS;var Ln="SelectContent",_m=g.forwardRef((e,t)=>{const n=gn(Ln,e.__scopeSelect),[r,o]=g.useState();if(Ne(()=>{o(new DocumentFragment)},[]),!n.open){const i=r;return i?Er.createPortal(A.jsx(Cm,{scope:e.__scopeSelect,children:A.jsx(kl.Slot,{scope:e.__scopeSelect,children:A.jsx("div",{children:e.children})})}),i):null}return A.jsx(Em,{...e,ref:t})});_m.displayName=Ln;var at=10,[Cm,vn]=Rr(Ln),ex="SelectContentImpl",tx=Eo("SelectContent.RemoveScroll"),Em=g.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:s,sideOffset:a,align:u,alignOffset:d,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:w,hideWhenDetached:f,avoidCollisions:S,...p}=e,c=gn(Ln,n),[v,x]=g.useState(null),[C,R]=g.useState(null),_=pe(t,V=>x(V)),[P,z]=g.useState(null),[E,O]=g.useState(null),N=bl(n),[D,k]=g.useState(!1),U=g.useRef(!1);g.useEffect(()=>{if(v)return f1(v)},[v]),b0();const j=g.useCallback(V=>{const[J,...G]=N().map(X=>X.ref.current),[W]=G.slice(-1),H=document.activeElement;for(const X of V)if(X===H||(X==null||X.scrollIntoView({block:"nearest"}),X===J&&C&&(C.scrollTop=0),X===W&&C&&(C.scrollTop=C.scrollHeight),X==null||X.focus(),document.activeElement!==H))return},[N,C]),$=g.useCallback(()=>j([P,v]),[j,P,v]);g.useEffect(()=>{D&&$()},[D,$]);const{onOpenChange:b,triggerPointerDownPosRef:I}=c;g.useEffect(()=>{if(v){let V={x:0,y:0};const J=W=>{var H,X;V={x:Math.abs(Math.round(W.pageX)-(((H=I.current)==null?void 0:H.x)??0)),y:Math.abs(Math.round(W.pageY)-(((X=I.current)==null?void 0:X.y)??0))}},G=W=>{V.x<=10&&V.y<=10?W.preventDefault():v.contains(W.target)||b(!1),document.removeEventListener("pointermove",J),I.current=null};return I.current!==null&&(document.addEventListener("pointermove",J),document.addEventListener("pointerup",G,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",J),document.removeEventListener("pointerup",G,{capture:!0})}}},[v,b,I]),g.useEffect(()=>{const V=()=>b(!1);return window.addEventListener("blur",V),window.addEventListener("resize",V),()=>{window.removeEventListener("blur",V),window.removeEventListener("resize",V)}},[b]);const[F,B]=Bm(V=>{const J=N().filter(H=>!H.disabled),G=J.find(H=>H.ref.current===document.activeElement),W=$m(J,V,G);W&&setTimeout(()=>W.ref.current.focus())}),q=g.useCallback((V,J,G)=>{const W=!U.current&&!G;(c.value!==void 0&&c.value===J||W)&&(z(V),W&&(U.current=!0))},[c.value]),Ee=g.useCallback(()=>v==null?void 0:v.focus(),[v]),ve=g.useCallback((V,J,G)=>{const W=!U.current&&!G;(c.value!==void 0&&c.value===J||W)&&O(V)},[c.value]),Ze=r==="popper"?wa:km,he=Ze===wa?{side:s,sideOffset:a,align:u,alignOffset:d,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:w,hideWhenDetached:f,avoidCollisions:S}:{};return A.jsx(Cm,{scope:n,content:v,viewport:C,onViewportChange:R,itemRefCallback:q,selectedItem:P,onItemLeave:Ee,itemTextRefCallback:ve,focusSelectedItem:$,selectedItemText:E,position:r,isPositioned:D,searchRef:F,children:A.jsx(Lh,{as:tx,allowPinchZoom:!0,children:A.jsx(Eh,{asChild:!0,trapped:c.open,onMountAutoFocus:V=>{V.preventDefault()},onUnmountAutoFocus:ee(o,V=>{var J;(J=c.trigger)==null||J.focus({preventScroll:!0}),V.preventDefault()}),children:A.jsx(ku,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:V=>V.preventDefault(),onDismiss:()=>c.onOpenChange(!1),children:A.jsx(Ze,{role:"listbox",id:c.contentId,"data-state":c.open?"open":"closed",dir:c.dir,onContextMenu:V=>V.preventDefault(),...p,...he,onPlaced:()=>k(!0),ref:_,style:{display:"flex",flexDirection:"column",outline:"none",...p.style},onKeyDown:ee(p.onKeyDown,V=>{const J=V.ctrlKey||V.altKey||V.metaKey;if(V.key==="Tab"&&V.preventDefault(),!J&&V.key.length===1&&B(V.key),["ArrowUp","ArrowDown","Home","End"].includes(V.key)){let W=N().filter(H=>!H.disabled).map(H=>H.ref.current);if(["ArrowUp","End"].includes(V.key)&&(W=W.slice().reverse()),["ArrowUp","ArrowDown"].includes(V.key)){const H=V.target,X=W.indexOf(H);W=W.slice(X+1)}setTimeout(()=>j(W)),V.preventDefault()}})})})})})})});Em.displayName=ex;var nx="SelectItemAlignedPosition",km=g.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,i=gn(Ln,n),l=vn(Ln,n),[s,a]=g.useState(null),[u,d]=g.useState(null),m=pe(t,_=>d(_)),h=bl(n),y=g.useRef(!1),w=g.useRef(!0),{viewport:f,selectedItem:S,selectedItemText:p,focusSelectedItem:c}=l,v=g.useCallback(()=>{if(i.trigger&&i.valueNode&&s&&u&&f&&S&&p){const _=i.trigger.getBoundingClientRect(),P=u.getBoundingClientRect(),z=i.valueNode.getBoundingClientRect(),E=p.getBoundingClientRect();if(i.dir!=="rtl"){const H=E.left-P.left,X=z.left-H,De=_.left-X,Je=_.width+De,Nr=Math.max(Je,P.width),Ar=window.innerWidth-at,Tr=bd(X,[at,Math.max(at,Ar-Nr)]);s.style.minWidth=Je+"px",s.style.left=Tr+"px"}else{const H=P.right-E.right,X=window.innerWidth-z.right-H,De=window.innerWidth-_.right-X,Je=_.width+De,Nr=Math.max(Je,P.width),Ar=window.innerWidth-at,Tr=bd(X,[at,Math.max(at,Ar-Nr)]);s.style.minWidth=Je+"px",s.style.right=Tr+"px"}const O=h(),N=window.innerHeight-at*2,D=f.scrollHeight,k=window.getComputedStyle(u),U=parseInt(k.borderTopWidth,10),j=parseInt(k.paddingTop,10),$=parseInt(k.borderBottomWidth,10),b=parseInt(k.paddingBottom,10),I=U+j+D+b+$,F=Math.min(S.offsetHeight*5,I),B=window.getComputedStyle(f),q=parseInt(B.paddingTop,10),Ee=parseInt(B.paddingBottom,10),ve=_.top+_.height/2-at,Ze=N-ve,he=S.offsetHeight/2,V=S.offsetTop+he,J=U+j+V,G=I-J;if(J<=ve){const H=O.length>0&&S===O[O.length-1].ref.current;s.style.bottom="0px";const X=u.clientHeight-f.offsetTop-f.offsetHeight,De=Math.max(Ze,he+(H?Ee:0)+X+$),Je=J+De;s.style.height=Je+"px"}else{const H=O.length>0&&S===O[0].ref.current;s.style.top="0px";const De=Math.max(ve,U+f.offsetTop+(H?q:0)+he)+G;s.style.height=De+"px",f.scrollTop=J-ve+f.offsetTop}s.style.margin=`${at}px 0`,s.style.minHeight=F+"px",s.style.maxHeight=N+"px",r==null||r(),requestAnimationFrame(()=>y.current=!0)}},[h,i.trigger,i.valueNode,s,u,f,S,p,i.dir,r]);Ne(()=>v(),[v]);const[x,C]=g.useState();Ne(()=>{u&&C(window.getComputedStyle(u).zIndex)},[u]);const R=g.useCallback(_=>{_&&w.current===!0&&(v(),c==null||c(),w.current=!1)},[v,c]);return A.jsx(ox,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:y,onScrollButtonChange:R,children:A.jsx("div",{ref:a,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:x},children:A.jsx(ce.div,{...o,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});km.displayName=nx;var rx="SelectPopperPosition",wa=g.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=at,...i}=e,l=Pl(n);return A.jsx(nm,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});wa.displayName=rx;var[ox,ju]=Rr(Ln,{}),Sa="SelectViewport",bm=g.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,i=vn(Sa,n),l=ju(Sa,n),s=pe(t,i.onViewportChange),a=g.useRef(0);return A.jsxs(A.Fragment,{children:[A.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),A.jsx(kl.Slot,{scope:n,children:A.jsx(ce.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:ee(o.onScroll,u=>{const d=u.currentTarget,{contentWrapper:m,shouldExpandOnScrollRef:h}=l;if(h!=null&&h.current&&m){const y=Math.abs(a.current-d.scrollTop);if(y>0){const w=window.innerHeight-at*2,f=parseFloat(m.style.minHeight),S=parseFloat(m.style.height),p=Math.max(f,S);if(p<w){const c=p+y,v=Math.min(w,c),x=c-v;m.style.height=v+"px",m.style.bottom==="0px"&&(d.scrollTop=x>0?x:0,m.style.justifyContent="flex-end")}}}a.current=d.scrollTop})})})]})});bm.displayName=Sa;var Pm="SelectGroup",[ix,lx]=Rr(Pm),sx=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=vl();return A.jsx(ix,{scope:n,id:o,children:A.jsx(ce.div,{role:"group","aria-labelledby":o,...r,ref:t})})});sx.displayName=Pm;var Rm="SelectLabel",Nm=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=lx(Rm,n);return A.jsx(ce.div,{id:o.id,...r,ref:t})});Nm.displayName=Rm;var Ji="SelectItem",[ax,Am]=Rr(Ji),Tm=g.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...l}=e,s=gn(Ji,n),a=vn(Ji,n),u=s.value===r,[d,m]=g.useState(i??""),[h,y]=g.useState(!1),w=pe(t,c=>{var v;return(v=a.itemRefCallback)==null?void 0:v.call(a,c,r,o)}),f=vl(),S=g.useRef("touch"),p=()=>{o||(s.onValueChange(r),s.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return A.jsx(ax,{scope:n,value:r,disabled:o,textId:f,isSelected:u,onItemTextChange:g.useCallback(c=>{m(v=>v||((c==null?void 0:c.textContent)??"").trim())},[]),children:A.jsx(kl.ItemSlot,{scope:n,value:r,disabled:o,textValue:d,children:A.jsx(ce.div,{role:"option","aria-labelledby":f,"data-highlighted":h?"":void 0,"aria-selected":u&&h,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...l,ref:w,onFocus:ee(l.onFocus,()=>y(!0)),onBlur:ee(l.onBlur,()=>y(!1)),onClick:ee(l.onClick,()=>{S.current!=="mouse"&&p()}),onPointerUp:ee(l.onPointerUp,()=>{S.current==="mouse"&&p()}),onPointerDown:ee(l.onPointerDown,c=>{S.current=c.pointerType}),onPointerMove:ee(l.onPointerMove,c=>{var v;S.current=c.pointerType,o?(v=a.onItemLeave)==null||v.call(a):S.current==="mouse"&&c.currentTarget.focus({preventScroll:!0})}),onPointerLeave:ee(l.onPointerLeave,c=>{var v;c.currentTarget===document.activeElement&&((v=a.onItemLeave)==null||v.call(a))}),onKeyDown:ee(l.onKeyDown,c=>{var x;((x=a.searchRef)==null?void 0:x.current)!==""&&c.key===" "||(GS.includes(c.key)&&p(),c.key===" "&&c.preventDefault())})})})})});Tm.displayName=Ji;var Gr="SelectItemText",Om=g.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...i}=e,l=gn(Gr,n),s=vn(Gr,n),a=Am(Gr,n),u=ZS(Gr,n),[d,m]=g.useState(null),h=pe(t,p=>m(p),a.onItemTextChange,p=>{var c;return(c=s.itemTextRefCallback)==null?void 0:c.call(s,p,a.value,a.disabled)}),y=d==null?void 0:d.textContent,w=g.useMemo(()=>A.jsx("option",{value:a.value,disabled:a.disabled,children:y},a.value),[a.disabled,a.value,y]),{onNativeOptionAdd:f,onNativeOptionRemove:S}=u;return Ne(()=>(f(w),()=>S(w)),[f,S,w]),A.jsxs(A.Fragment,{children:[A.jsx(ce.span,{id:a.textId,...i,ref:h}),a.isSelected&&l.valueNode&&!l.valueNodeHasChildren?Er.createPortal(i.children,l.valueNode):null]})});Om.displayName=Gr;var Lm="SelectItemIndicator",Dm=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return Am(Lm,n).isSelected?A.jsx(ce.span,{"aria-hidden":!0,...r,ref:t}):null});Dm.displayName=Lm;var xa="SelectScrollUpButton",Im=g.forwardRef((e,t)=>{const n=vn(xa,e.__scopeSelect),r=ju(xa,e.__scopeSelect),[o,i]=g.useState(!1),l=pe(t,r.onScrollButtonChange);return Ne(()=>{if(n.viewport&&n.isPositioned){let s=function(){const u=a.scrollTop>0;i(u)};const a=n.viewport;return s(),a.addEventListener("scroll",s),()=>a.removeEventListener("scroll",s)}},[n.viewport,n.isPositioned]),o?A.jsx(Mm,{...e,ref:l,onAutoScroll:()=>{const{viewport:s,selectedItem:a}=n;s&&a&&(s.scrollTop=s.scrollTop-a.offsetHeight)}}):null});Im.displayName=xa;var _a="SelectScrollDownButton",zm=g.forwardRef((e,t)=>{const n=vn(_a,e.__scopeSelect),r=ju(_a,e.__scopeSelect),[o,i]=g.useState(!1),l=pe(t,r.onScrollButtonChange);return Ne(()=>{if(n.viewport&&n.isPositioned){let s=function(){const u=a.scrollHeight-a.clientHeight,d=Math.ceil(a.scrollTop)<u;i(d)};const a=n.viewport;return s(),a.addEventListener("scroll",s),()=>a.removeEventListener("scroll",s)}},[n.viewport,n.isPositioned]),o?A.jsx(Mm,{...e,ref:l,onAutoScroll:()=>{const{viewport:s,selectedItem:a}=n;s&&a&&(s.scrollTop=s.scrollTop+a.offsetHeight)}}):null});zm.displayName=_a;var Mm=g.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,i=vn("SelectScrollButton",n),l=g.useRef(null),s=bl(n),a=g.useCallback(()=>{l.current!==null&&(window.clearInterval(l.current),l.current=null)},[]);return g.useEffect(()=>()=>a(),[a]),Ne(()=>{var d;const u=s().find(m=>m.ref.current===document.activeElement);(d=u==null?void 0:u.ref.current)==null||d.scrollIntoView({block:"nearest"})},[s]),A.jsx(ce.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:ee(o.onPointerDown,()=>{l.current===null&&(l.current=window.setInterval(r,50))}),onPointerMove:ee(o.onPointerMove,()=>{var u;(u=i.onItemLeave)==null||u.call(i),l.current===null&&(l.current=window.setInterval(r,50))}),onPointerLeave:ee(o.onPointerLeave,()=>{a()})})}),ux="SelectSeparator",Fm=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return A.jsx(ce.div,{"aria-hidden":!0,...r,ref:t})});Fm.displayName=ux;var Ca="SelectArrow",cx=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Pl(n),i=gn(Ca,n),l=vn(Ca,n);return i.open&&l.position==="popper"?A.jsx(rm,{...o,...r,ref:t}):null});cx.displayName=Ca;var dx="SelectBubbleInput",jm=g.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{const o=g.useRef(null),i=pe(r,o),l=WS(t);return g.useEffect(()=>{const s=o.current;if(!s)return;const a=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(a,"value").set;if(l!==t&&d){const m=new Event("change",{bubbles:!0});d.call(s,t),s.dispatchEvent(m)}},[l,t]),A.jsx(ce.select,{...n,style:{...om,...n.style},ref:i,defaultValue:t})});jm.displayName=dx;function Um(e){return e===""||e===void 0}function Bm(e){const t=An(e),n=g.useRef(""),r=g.useRef(0),o=g.useCallback(l=>{const s=n.current+l;t(s),function a(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>a(""),1e3))}(s)},[t]),i=g.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return g.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function $m(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let l=fx(e,Math.max(i,0));o.length===1&&(l=l.filter(u=>u!==n));const a=l.find(u=>u.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}function fx(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var px=mm,Vm=vm,hx=wm,mx=Sm,gx=xm,Hm=_m,vx=bm,Wm=Nm,Km=Tm,yx=Om,wx=Dm,Gm=Im,Qm=zm,Ym=Fm;const Gx=px,Qx=hx,Sx=g.forwardRef(({className:e,children:t,...n},r)=>A.jsxs(Vm,{ref:r,className:kt("flex w-full items-center justify-between whitespace-nowrap rounded-md bg-transparent p-0 text-xs shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,A.jsx(mx,{asChild:!0,children:A.jsx(gh,{className:"h-3 w-3 ml-0.5 mt-0.5 opacity-50"})})]}));Sx.displayName=Vm.displayName;const Xm=g.forwardRef(({className:e,...t},n)=>A.jsx(Gm,{ref:n,className:kt("flex cursor-default items-center justify-center py-1",e),...t,children:A.jsx(yw,{className:"h-4 w-4"})}));Xm.displayName=Gm.displayName;const Zm=g.forwardRef(({className:e,...t},n)=>A.jsx(Qm,{ref:n,className:kt("flex cursor-default items-center justify-center py-1",e),...t,children:A.jsx(gh,{className:"h-4 w-4"})}));Zm.displayName=Qm.displayName;const xx=g.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>A.jsx(gx,{children:A.jsxs(Hm,{ref:o,className:kt("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[A.jsx(Xm,{}),A.jsx(vx,{className:kt("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),A.jsx(Zm,{})]})}));xx.displayName=Hm.displayName;const _x=g.forwardRef(({className:e,...t},n)=>A.jsx(Wm,{ref:n,className:kt("px-2 py-1.5 text-sm font-semibold",e),...t}));_x.displayName=Wm.displayName;const Cx=g.forwardRef(({className:e,children:t,...n},r)=>A.jsxs(Km,{ref:r,className:kt("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[A.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:A.jsx(wx,{children:A.jsx(vw,{className:"h-4 w-4"})})}),A.jsx(yx,{children:t})]}));Cx.displayName=Km.displayName;const Ex=g.forwardRef(({className:e,...t},n)=>A.jsx(Ym,{ref:n,className:kt("-mx-1 my-1 h-px bg-muted",e),...t}));Ex.displayName=Ym.displayName;var Pd;(function(e){e[e.Audio=1]="Audio",e[e.Cache=2]="Cache",e[e.Config=3]="Config",e[e.Data=4]="Data",e[e.LocalData=5]="LocalData",e[e.Document=6]="Document",e[e.Download=7]="Download",e[e.Picture=8]="Picture",e[e.Public=9]="Public",e[e.Video=10]="Video",e[e.Resource=11]="Resource",e[e.Temp=12]="Temp",e[e.AppConfig=13]="AppConfig",e[e.AppData=14]="AppData",e[e.AppLocalData=15]="AppLocalData",e[e.AppCache=16]="AppCache",e[e.AppLog=17]="AppLog",e[e.Desktop=18]="Desktop",e[e.Executable=19]="Executable",e[e.Font=20]="Font",e[e.Home=21]="Home",e[e.Runtime=22]="Runtime",e[e.Template=23]="Template"})(Pd||(Pd={}));var Rd;(function(e){e[e.Start=0]="Start",e[e.Current=1]="Current",e[e.End=2]="End"})(Rd||(Rd={}));function kx(e){return{isFile:e.isFile,isDirectory:e.isDirectory,isSymlink:e.isSymlink,size:e.size,mtime:e.mtime!==null?new Date(e.mtime):null,atime:e.atime!==null?new Date(e.atime):null,birthtime:e.birthtime!==null?new Date(e.birthtime):null,readonly:e.readonly,fileAttributes:e.fileAttributes,dev:e.dev,ino:e.ino,mode:e.mode,nlink:e.nlink,uid:e.uid,gid:e.gid,rdev:e.rdev,blksize:e.blksize,blocks:e.blocks}}function bx(e){const t=new Uint8ClampedArray(e),n=t.byteLength;let r=0;for(let o=0;o<n;o++){const i=t[o];r*=256,r+=i}return r}class Px extends Yp{async read(t){if(t.byteLength===0)return 0;const n=await T("plugin:fs|read",{rid:this.rid,len:t.byteLength}),r=bx(n.slice(-8)),o=n instanceof ArrayBuffer?new Uint8Array(n):n;return t.set(o.slice(0,o.length-8)),r===0?null:r}async seek(t,n){return await T("plugin:fs|seek",{rid:this.rid,offset:t,whence:n})}async stat(){const t=await T("plugin:fs|fstat",{rid:this.rid});return kx(t)}async truncate(t){await T("plugin:fs|ftruncate",{rid:this.rid,len:t})}async write(t){return await T("plugin:fs|write",{rid:this.rid,data:t})}}async function Rx(e,t){if(e instanceof URL&&e.protocol!=="file:")throw new TypeError("Must be a file URL.");const n=await T("plugin:fs|open",{path:e instanceof URL?e.toString():e,options:t});return new Px(n)}async function Yx(e,t){if(e instanceof URL&&e.protocol!=="file:")throw new TypeError("Must be a file URL.");const n=await T("plugin:fs|read_file",{path:e instanceof URL?e.toString():e,options:t});return n instanceof ArrayBuffer?new Uint8Array(n):Uint8Array.from(n)}async function Xx(e,t,n){if(e instanceof URL&&e.protocol!=="file:")throw new TypeError("Must be a file URL.");if(t instanceof ReadableStream){const r=await Rx(e,n);for await(const o of t)await r.write(o);await r.close()}else await T("plugin:fs|write_file",t,{headers:{path:encodeURIComponent(e instanceof URL?e.toString():e),options:JSON.stringify(n)}})}class Jm{constructor(...t){this.type="Logical",t.length===1?"Logical"in t[0]?(this.width=t[0].Logical.width,this.height=t[0].Logical.height):(this.width=t[0].width,this.height=t[0].height):(this.width=t[0],this.height=t[1])}toPhysical(t){return new dr(this.width*t,this.height*t)}[Be](){return{width:this.width,height:this.height}}toJSON(){return this[Be]()}}class dr{constructor(...t){this.type="Physical",t.length===1?"Physical"in t[0]?(this.width=t[0].Physical.width,this.height=t[0].Physical.height):(this.width=t[0].width,this.height=t[0].height):(this.width=t[0],this.height=t[1])}toLogical(t){return new Jm(this.width/t,this.height/t)}[Be](){return{width:this.width,height:this.height}}toJSON(){return this[Be]()}}class Qt{constructor(t){this.size=t}toLogical(t){return this.size instanceof Jm?this.size:this.size.toLogical(t)}toPhysical(t){return this.size instanceof dr?this.size:this.size.toPhysical(t)}[Be](){return{[`${this.size.type}`]:{width:this.size.width,height:this.size.height}}}toJSON(){return this[Be]()}}class qm{constructor(...t){this.type="Logical",t.length===1?"Logical"in t[0]?(this.x=t[0].Logical.x,this.y=t[0].Logical.y):(this.x=t[0].x,this.y=t[0].y):(this.x=t[0],this.y=t[1])}toPhysical(t){return new tt(this.x*t,this.y*t)}[Be](){return{x:this.x,y:this.y}}toJSON(){return this[Be]()}}class tt{constructor(...t){this.type="Physical",t.length===1?"Physical"in t[0]?(this.x=t[0].Physical.x,this.y=t[0].Physical.y):(this.x=t[0].x,this.y=t[0].y):(this.x=t[0],this.y=t[1])}toLogical(t){return new qm(this.x/t,this.y/t)}[Be](){return{x:this.x,y:this.y}}toJSON(){return this[Be]()}}class tr{constructor(t){this.position=t}toLogical(t){return this.position instanceof qm?this.position:this.position.toLogical(t)}toPhysical(t){return this.position instanceof tt?this.position:this.position.toPhysical(t)}[Be](){return{[`${this.position.type}`]:{x:this.position.x,y:this.position.y}}}toJSON(){return this[Be]()}}class io extends Yp{constructor(t){super(t)}static async new(t,n,r){return T("plugin:image|new",{rgba:qi(t),width:n,height:r}).then(o=>new io(o))}static async fromBytes(t){return T("plugin:image|from_bytes",{bytes:qi(t)}).then(n=>new io(n))}static async fromPath(t){return T("plugin:image|from_path",{path:t}).then(n=>new io(n))}async rgba(){return T("plugin:image|rgba",{rid:this.rid}).then(t=>new Uint8Array(t))}async size(){return T("plugin:image|size",{rid:this.rid})}}function qi(e){return e==null?null:typeof e=="string"?e:e instanceof io?e.rid:e}var Ea;(function(e){e[e.Critical=1]="Critical",e[e.Informational=2]="Informational"})(Ea||(Ea={}));class Nx{constructor(t){this._preventDefault=!1,this.event=t.event,this.id=t.id}preventDefault(){this._preventDefault=!0}isPreventDefault(){return this._preventDefault}}var Nd;(function(e){e.None="none",e.Normal="normal",e.Indeterminate="indeterminate",e.Paused="paused",e.Error="error"})(Nd||(Nd={}));function eg(){return new Uu(window.__TAURI_INTERNALS__.metadata.currentWindow.label,{skip:!0})}async function hs(){return T("plugin:window|get_all_windows").then(e=>e.map(t=>new Uu(t,{skip:!0})))}const ms=["tauri://created","tauri://error"];class Uu{constructor(t,n={}){var r;this.label=t,this.listeners=Object.create(null),n!=null&&n.skip||T("plugin:window|create",{options:{...n,parent:typeof n.parent=="string"?n.parent:(r=n.parent)===null||r===void 0?void 0:r.label,label:t}}).then(async()=>this.emit("tauri://created")).catch(async o=>this.emit("tauri://error",o))}static async getByLabel(t){var n;return(n=(await hs()).find(r=>r.label===t))!==null&&n!==void 0?n:null}static getCurrent(){return eg()}static async getAll(){return hs()}static async getFocusedWindow(){for(const t of await hs())if(await t.isFocused())return t;return null}async listen(t,n){return this._handleTauriEvent(t,n)?()=>{const r=this.listeners[t];r.splice(r.indexOf(n),1)}:_u(t,n,{target:{kind:"Window",label:this.label}})}async once(t,n){return this._handleTauriEvent(t,n)?()=>{const r=this.listeners[t];r.splice(r.indexOf(n),1)}:ah(t,n,{target:{kind:"Window",label:this.label}})}async emit(t,n){if(ms.includes(t)){for(const r of this.listeners[t]||[])r({event:t,id:-1,payload:n});return}return uh(t,n)}async emitTo(t,n,r){if(ms.includes(n)){for(const o of this.listeners[n]||[])o({event:n,id:-1,payload:r});return}return ch(t,n,r)}_handleTauriEvent(t,n){return ms.includes(t)?(t in this.listeners?this.listeners[t].push(n):this.listeners[t]=[n],!0):!1}async scaleFactor(){return T("plugin:window|scale_factor",{label:this.label})}async innerPosition(){return T("plugin:window|inner_position",{label:this.label}).then(t=>new tt(t))}async outerPosition(){return T("plugin:window|outer_position",{label:this.label}).then(t=>new tt(t))}async innerSize(){return T("plugin:window|inner_size",{label:this.label}).then(t=>new dr(t))}async outerSize(){return T("plugin:window|outer_size",{label:this.label}).then(t=>new dr(t))}async isFullscreen(){return T("plugin:window|is_fullscreen",{label:this.label})}async isMinimized(){return T("plugin:window|is_minimized",{label:this.label})}async isMaximized(){return T("plugin:window|is_maximized",{label:this.label})}async isFocused(){return T("plugin:window|is_focused",{label:this.label})}async isDecorated(){return T("plugin:window|is_decorated",{label:this.label})}async isResizable(){return T("plugin:window|is_resizable",{label:this.label})}async isMaximizable(){return T("plugin:window|is_maximizable",{label:this.label})}async isMinimizable(){return T("plugin:window|is_minimizable",{label:this.label})}async isClosable(){return T("plugin:window|is_closable",{label:this.label})}async isVisible(){return T("plugin:window|is_visible",{label:this.label})}async title(){return T("plugin:window|title",{label:this.label})}async theme(){return T("plugin:window|theme",{label:this.label})}async center(){return T("plugin:window|center",{label:this.label})}async requestUserAttention(t){let n=null;return t&&(t===Ea.Critical?n={type:"Critical"}:n={type:"Informational"}),T("plugin:window|request_user_attention",{label:this.label,value:n})}async setResizable(t){return T("plugin:window|set_resizable",{label:this.label,value:t})}async setEnabled(t){return T("plugin:window|set_enabled",{label:this.label,value:t})}async isEnabled(){return T("plugin:window|is_enabled",{label:this.label})}async setMaximizable(t){return T("plugin:window|set_maximizable",{label:this.label,value:t})}async setMinimizable(t){return T("plugin:window|set_minimizable",{label:this.label,value:t})}async setClosable(t){return T("plugin:window|set_closable",{label:this.label,value:t})}async setTitle(t){return T("plugin:window|set_title",{label:this.label,value:t})}async maximize(){return T("plugin:window|maximize",{label:this.label})}async unmaximize(){return T("plugin:window|unmaximize",{label:this.label})}async toggleMaximize(){return T("plugin:window|toggle_maximize",{label:this.label})}async minimize(){return T("plugin:window|minimize",{label:this.label})}async unminimize(){return T("plugin:window|unminimize",{label:this.label})}async show(){return T("plugin:window|show",{label:this.label})}async hide(){return T("plugin:window|hide",{label:this.label})}async close(){return T("plugin:window|close",{label:this.label})}async destroy(){return T("plugin:window|destroy",{label:this.label})}async setDecorations(t){return T("plugin:window|set_decorations",{label:this.label,value:t})}async setShadow(t){return T("plugin:window|set_shadow",{label:this.label,value:t})}async setEffects(t){return T("plugin:window|set_effects",{label:this.label,value:t})}async clearEffects(){return T("plugin:window|set_effects",{label:this.label,value:null})}async setAlwaysOnTop(t){return T("plugin:window|set_always_on_top",{label:this.label,value:t})}async setAlwaysOnBottom(t){return T("plugin:window|set_always_on_bottom",{label:this.label,value:t})}async setContentProtected(t){return T("plugin:window|set_content_protected",{label:this.label,value:t})}async setSize(t){return T("plugin:window|set_size",{label:this.label,value:t instanceof Qt?t:new Qt(t)})}async setMinSize(t){return T("plugin:window|set_min_size",{label:this.label,value:t instanceof Qt?t:t?new Qt(t):null})}async setMaxSize(t){return T("plugin:window|set_max_size",{label:this.label,value:t instanceof Qt?t:t?new Qt(t):null})}async setSizeConstraints(t){function n(r){return r?{Logical:r}:null}return T("plugin:window|set_size_constraints",{label:this.label,value:{minWidth:n(t==null?void 0:t.minWidth),minHeight:n(t==null?void 0:t.minHeight),maxWidth:n(t==null?void 0:t.maxWidth),maxHeight:n(t==null?void 0:t.maxHeight)}})}async setPosition(t){return T("plugin:window|set_position",{label:this.label,value:t instanceof tr?t:new tr(t)})}async setFullscreen(t){return T("plugin:window|set_fullscreen",{label:this.label,value:t})}async setFocus(){return T("plugin:window|set_focus",{label:this.label})}async setIcon(t){return T("plugin:window|set_icon",{label:this.label,value:qi(t)})}async setSkipTaskbar(t){return T("plugin:window|set_skip_taskbar",{label:this.label,value:t})}async setCursorGrab(t){return T("plugin:window|set_cursor_grab",{label:this.label,value:t})}async setCursorVisible(t){return T("plugin:window|set_cursor_visible",{label:this.label,value:t})}async setCursorIcon(t){return T("plugin:window|set_cursor_icon",{label:this.label,value:t})}async setBackgroundColor(t){return T("plugin:window|set_background_color",{color:t})}async setCursorPosition(t){return T("plugin:window|set_cursor_position",{label:this.label,value:t instanceof tr?t:new tr(t)})}async setIgnoreCursorEvents(t){return T("plugin:window|set_ignore_cursor_events",{label:this.label,value:t})}async startDragging(){return T("plugin:window|start_dragging",{label:this.label})}async startResizeDragging(t){return T("plugin:window|start_resize_dragging",{label:this.label,value:t})}async setBadgeCount(t){return T("plugin:window|set_badge_count",{label:this.label,value:t})}async setBadgeLabel(t){return T("plugin:window|set_badge_label",{label:this.label,value:t})}async setOverlayIcon(t){return T("plugin:window|set_overlay_icon",{label:this.label,value:t?qi(t):void 0})}async setProgressBar(t){return T("plugin:window|set_progress_bar",{label:this.label,value:t})}async setVisibleOnAllWorkspaces(t){return T("plugin:window|set_visible_on_all_workspaces",{label:this.label,value:t})}async setTitleBarStyle(t){return T("plugin:window|set_title_bar_style",{label:this.label,value:t})}async setTheme(t){return T("plugin:window|set_theme",{label:this.label,value:t})}async onResized(t){return this.listen(xe.WINDOW_RESIZED,n=>{n.payload=new dr(n.payload),t(n)})}async onMoved(t){return this.listen(xe.WINDOW_MOVED,n=>{n.payload=new tt(n.payload),t(n)})}async onCloseRequested(t){return this.listen(xe.WINDOW_CLOSE_REQUESTED,async n=>{const r=new Nx(n);await t(r),r.isPreventDefault()||await this.destroy()})}async onDragDropEvent(t){const n=await this.listen(xe.DRAG_ENTER,l=>{t({...l,payload:{type:"enter",paths:l.payload.paths,position:new tt(l.payload.position)}})}),r=await this.listen(xe.DRAG_OVER,l=>{t({...l,payload:{type:"over",position:new tt(l.payload.position)}})}),o=await this.listen(xe.DRAG_DROP,l=>{t({...l,payload:{type:"drop",paths:l.payload.paths,position:new tt(l.payload.position)}})}),i=await this.listen(xe.DRAG_LEAVE,l=>{t({...l,payload:{type:"leave"}})});return()=>{n(),o(),r(),i()}}async onFocusChanged(t){const n=await this.listen(xe.WINDOW_FOCUS,o=>{t({...o,payload:!0})}),r=await this.listen(xe.WINDOW_BLUR,o=>{t({...o,payload:!1})});return()=>{n(),r()}}async onScaleChanged(t){return this.listen(xe.WINDOW_SCALE_FACTOR_CHANGED,t)}async onThemeChanged(t){return this.listen(xe.WINDOW_THEME_CHANGED,t)}}var Ad;(function(e){e.AppearanceBased="appearanceBased",e.Light="light",e.Dark="dark",e.MediumLight="mediumLight",e.UltraDark="ultraDark",e.Titlebar="titlebar",e.Selection="selection",e.Menu="menu",e.Popover="popover",e.Sidebar="sidebar",e.HeaderView="headerView",e.Sheet="sheet",e.WindowBackground="windowBackground",e.HudWindow="hudWindow",e.FullScreenUI="fullScreenUI",e.Tooltip="tooltip",e.ContentBackground="contentBackground",e.UnderWindowBackground="underWindowBackground",e.UnderPageBackground="underPageBackground",e.Mica="mica",e.Blur="blur",e.Acrylic="acrylic",e.Tabbed="tabbed",e.TabbedDark="tabbedDark",e.TabbedLight="tabbedLight"})(Ad||(Ad={}));var Td;(function(e){e.FollowsWindowActiveState="followsWindowActiveState",e.Active="active",e.Inactive="inactive"})(Td||(Td={}));function Ax(){return new tg(eg(),window.__TAURI_INTERNALS__.metadata.currentWebview.label,{skip:!0})}async function Od(){return T("plugin:webview|get_all_webviews").then(e=>e.map(t=>new tg(new Uu(t.windowLabel,{skip:!0}),t.label,{skip:!0})))}const gs=["tauri://created","tauri://error"];class tg{constructor(t,n,r){this.window=t,this.label=n,this.listeners=Object.create(null),r!=null&&r.skip||T("plugin:webview|create_webview",{windowLabel:t.label,label:n,options:r}).then(async()=>this.emit("tauri://created")).catch(async o=>this.emit("tauri://error",o))}static async getByLabel(t){var n;return(n=(await Od()).find(r=>r.label===t))!==null&&n!==void 0?n:null}static getCurrent(){return Ax()}static async getAll(){return Od()}async listen(t,n){return this._handleTauriEvent(t,n)?()=>{const r=this.listeners[t];r.splice(r.indexOf(n),1)}:_u(t,n,{target:{kind:"Webview",label:this.label}})}async once(t,n){return this._handleTauriEvent(t,n)?()=>{const r=this.listeners[t];r.splice(r.indexOf(n),1)}:ah(t,n,{target:{kind:"Webview",label:this.label}})}async emit(t,n){if(gs.includes(t)){for(const r of this.listeners[t]||[])r({event:t,id:-1,payload:n});return}return uh(t,n)}async emitTo(t,n,r){if(gs.includes(n)){for(const o of this.listeners[n]||[])o({event:n,id:-1,payload:r});return}return ch(t,n,r)}_handleTauriEvent(t,n){return gs.includes(t)?(t in this.listeners?this.listeners[t].push(n):this.listeners[t]=[n],!0):!1}async position(){return T("plugin:webview|webview_position",{label:this.label}).then(t=>new tt(t))}async size(){return T("plugin:webview|webview_size",{label:this.label}).then(t=>new dr(t))}async close(){return T("plugin:webview|close",{label:this.label})}async setSize(t){return T("plugin:webview|set_webview_size",{label:this.label,value:t instanceof Qt?t:new Qt(t)})}async setPosition(t){return T("plugin:webview|set_webview_position",{label:this.label,value:t instanceof tr?t:new tr(t)})}async setFocus(){return T("plugin:webview|set_webview_focus",{label:this.label})}async hide(){return T("plugin:webview|webview_hide",{label:this.label})}async show(){return T("plugin:webview|webview_show",{label:this.label})}async setZoom(t){return T("plugin:webview|set_webview_zoom",{label:this.label,value:t})}async reparent(t){return T("plugin:webview|reparent",{label:this.label,window:typeof t=="string"?t:t.label})}async clearAllBrowsingData(){return T("plugin:webview|clear_all_browsing_data")}async setBackgroundColor(t){return T("plugin:webview|set_webview_background_color",{color:t})}async onDragDropEvent(t){const n=await this.listen(xe.DRAG_ENTER,l=>{t({...l,payload:{type:"enter",paths:l.payload.paths,position:new tt(l.payload.position)}})}),r=await this.listen(xe.DRAG_OVER,l=>{t({...l,payload:{type:"over",position:new tt(l.payload.position)}})}),o=await this.listen(xe.DRAG_DROP,l=>{t({...l,payload:{type:"drop",paths:l.payload.paths,position:new tt(l.payload.position)}})}),i=await this.listen(xe.DRAG_LEAVE,l=>{t({...l,payload:{type:"leave"}})});return()=>{n(),o(),r(),i()}}}const Tx={theme:"system",setTheme:()=>null},Ox=g.createContext(Tx);function Zx({children:e,defaultTheme:t="system",storageKey:n="vite-ui-theme",...r}){const[o,i]=g.useState(()=>localStorage.getItem(n)||t);g.useEffect(()=>{const s=window.document.documentElement;if(s.classList.remove("light","dark"),o==="system"){const a=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";s.classList.add(a);return}s.classList.add(o)},[o]);const l={theme:o,setTheme:s=>{localStorage.setItem(n,s),i(s)}};return A.jsx(Ox.Provider,{...r,value:l,children:e})}export{nm as $,da as A,vl as B,Dx as C,ro as D,Xp as E,Eh as F,st as G,bu as H,Eo as I,pw as J,Jw as K,Hx as L,Wx as M,BS as N,$S as O,Mx as P,HS as Q,Yp as R,sw as S,Vx as T,An as U,Ux as V,WS as W,fS as X,vw as Y,_l as Z,tm as _,Ky as a,rm as a0,em as a1,Gx as a2,Sx as a3,Qx as a4,xx as a5,Cx as a6,gh as a7,Nt as a8,Er as a9,Xx as aa,Yx as ab,bd as ac,Oy as ad,Iy as ae,Ly as af,Uu as ag,tg as ah,Dy as ai,zy as aj,Ty as ak,jx as al,Zx as am,Hu as an,Ax as ao,ah as ap,Lx as aq,Ld as ar,lw as b,Si as c,Fx as d,Ay as e,zx as f,oo as g,Cu as h,T as i,A as j,kt as k,_u as l,ce as m,gl as n,pe as o,ee as p,Pu as q,g as r,$e as s,Lh as t,rw as u,f1 as v,Ix as w,b0 as x,ku as y,Bx as z};
