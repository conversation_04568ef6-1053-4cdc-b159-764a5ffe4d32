import e,{useRef as r,useMemo as t,useEffect as o,useState as a,useCallback as l,useLayoutEffect as n}from"react";function s(){return(s=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e}).apply(this,arguments)}function c(e,r){if(null==e)return{};var t,o,a={},l=Object.keys(e);for(o=0;o<l.length;o++)r.indexOf(t=l[o])>=0||(a[t]=e[t]);return a}function u(e){const t=r(e),o=r(e=>{t.current&&t.current(e)});return t.current=e,o.current}const i=(e,r=0,t=1)=>e>t?t:e<r?r:e,d=e=>"touches"in e,f=e=>e&&e.ownerDocument.defaultView||self,h=(e,r,t)=>{const o=e.getBoundingClientRect(),a=d(r)?((e,r)=>{for(let t=0;t<e.length;t++)if(e[t].identifier===r)return e[t];return e[0]})(r.touches,t):r;return{left:i((a.pageX-(o.left+f(e).pageXOffset))/o.width),top:i((a.pageY-(o.top+f(e).pageYOffset))/o.height)}},v=e=>{!d(e)&&e.preventDefault()},m=e.memo(a=>{let{onMove:l,onKey:n}=a,i=c(a,["onMove","onKey"]);const m=r(null),g=u(l),p=u(n),b=r(null),_=r(!1),[x,C,E]=t(()=>{const e=e=>{v(e),(d(e)?e.touches.length>0:e.buttons>0)&&m.current?g(h(m.current,e,b.current)):t(!1)},r=()=>t(!1);function t(t){const o=_.current,a=f(m.current),l=t?a.addEventListener:a.removeEventListener;l(o?"touchmove":"mousemove",e),l(o?"touchend":"mouseup",r)}return[({nativeEvent:e})=>{const r=m.current;if(r&&(v(e),!((e,r)=>r&&!d(e))(e,_.current)&&r)){if(d(e)){_.current=!0;const r=e.changedTouches||[];r.length&&(b.current=r[0].identifier)}r.focus(),g(h(r,e,b.current)),t(!0)}},e=>{const r=e.which||e.keyCode;r<37||r>40||(e.preventDefault(),p({left:39===r?.05:37===r?-.05:0,top:40===r?.05:38===r?-.05:0}))},t]},[p,g]);return o(()=>E,[E]),e.createElement("div",s({},i,{onTouchStart:x,onMouseDown:x,className:"react-colorful__interactive",ref:m,onKeyDown:C,tabIndex:0,role:"slider"}))}),g=e=>e.filter(Boolean).join(" "),p=({className:r,color:t,left:o,top:a=.5})=>{const l=g(["react-colorful__pointer",r]);return e.createElement("div",{className:l,style:{top:100*a+"%",left:100*o+"%"}},e.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:t}}))},b=(e,r=0,t=Math.pow(10,r))=>Math.round(t*e)/t,_={grad:.9,turn:360,rad:360/(2*Math.PI)},x=e=>K(C(e)),C=e=>("#"===e[0]&&(e=e.substring(1)),e.length<6?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:4===e.length?b(parseInt(e[3]+e[3],16)/255,2):1}:{r:parseInt(e.substring(0,2),16),g:parseInt(e.substring(2,4),16),b:parseInt(e.substring(4,6),16),a:8===e.length?b(parseInt(e.substring(6,8),16)/255,2):1}),E=(e,r="deg")=>Number(e)*(_[r]||1),H=e=>{const r=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return r?N({h:E(r[1],r[2]),s:Number(r[3]),l:Number(r[4]),a:void 0===r[5]?1:Number(r[5])/(r[6]?100:1)}):{h:0,s:0,v:0,a:1}},M=H,N=({h:e,s:r,l:t,a:o})=>({h:e,s:(r*=(t<50?t:100-t)/100)>0?2*r/(t+r)*100:0,v:t+r,a:o}),w=e=>D(k(e)),$=({h:e,s:r,v:t,a:o})=>{const a=(200-r)*t/100;return{h:b(e),s:b(a>0&&a<200?r*t/100/(a<=100?a:200-a)*100:0),l:b(a/2),a:b(o,2)}},y=e=>{const{h:r,s:t,l:o}=$(e);return`hsl(${r}, ${t}%, ${o}%)`},q=e=>{const{h:r,s:t,l:o,a}=$(e);return`hsla(${r}, ${t}%, ${o}%, ${a})`},k=({h:e,s:r,v:t,a:o})=>{e=e/360*6,r/=100,t/=100;const a=Math.floor(e),l=t*(1-r),n=t*(1-(e-a)*r),s=t*(1-(1-e+a)*r),c=a%6;return{r:b(255*[t,n,l,l,s,t][c]),g:b(255*[s,t,t,n,l,l][c]),b:b(255*[l,l,s,t,t,n][c]),a:b(o,2)}},I=e=>{const r=/hsva?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return r?L({h:E(r[1],r[2]),s:Number(r[3]),v:Number(r[4]),a:void 0===r[5]?1:Number(r[5])/(r[6]?100:1)}):{h:0,s:0,v:0,a:1}},O=I,j=e=>{const r=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return r?K({r:Number(r[1])/(r[2]?100/255:1),g:Number(r[3])/(r[4]?100/255:1),b:Number(r[5])/(r[6]?100/255:1),a:void 0===r[7]?1:Number(r[7])/(r[8]?100:1)}):{h:0,s:0,v:0,a:1}},z=j,B=e=>{const r=e.toString(16);return r.length<2?"0"+r:r},D=({r:e,g:r,b:t,a:o})=>{const a=o<1?B(b(255*o)):"";return"#"+B(e)+B(r)+B(t)+a},K=({r:e,g:r,b:t,a:o})=>{const a=Math.max(e,r,t),l=a-Math.min(e,r,t),n=l?a===e?(r-t)/l:a===r?2+(t-e)/l:4+(e-r)/l:0;return{h:b(60*(n<0?n+6:n)),s:b(a?l/a*100:0),v:b(a/255*100),a:o}},L=e=>({h:b(e.h),s:b(e.s),v:b(e.v),a:b(e.a,2)}),A=e.memo(({className:r,hue:t,onChange:o})=>{const a=g(["react-colorful__hue",r]);return e.createElement("div",{className:a},e.createElement(m,{onMove:e=>{o({h:360*e.left})},onKey:e=>{o({h:i(t+360*e.left,0,360)})},"aria-label":"Hue","aria-valuenow":b(t),"aria-valuemax":"360","aria-valuemin":"0"},e.createElement(p,{className:"react-colorful__hue-pointer",left:t/360,color:y({h:t,s:100,v:100,a:1})})))}),S=e.memo(({hsva:r,onChange:t})=>{const o={backgroundColor:y({h:r.h,s:100,v:100,a:1})};return e.createElement("div",{className:"react-colorful__saturation",style:o},e.createElement(m,{onMove:e=>{t({s:100*e.left,v:100-100*e.top})},onKey:e=>{t({s:i(r.s+100*e.left,0,100),v:i(r.v-100*e.top,0,100)})},"aria-label":"Color","aria-valuetext":`Saturation ${b(r.s)}%, Brightness ${b(r.v)}%`},e.createElement(p,{className:"react-colorful__saturation-pointer",top:1-r.v/100,left:r.s/100,color:y(r)})))}),T=(e,r)=>{if(e===r)return!0;for(const t in e)if(e[t]!==r[t])return!1;return!0},F=(e,r)=>e.replace(/\s/g,"")===r.replace(/\s/g,""),P=(e,r)=>e.toLowerCase()===r.toLowerCase()||T(C(e),C(r));function X(e,t,n){const s=u(n),[c,i]=a(()=>e.toHsva(t)),d=r({color:t,hsva:c});o(()=>{if(!e.equal(t,d.current.color)){const r=e.toHsva(t);d.current={hsva:r,color:t},i(r)}},[t,e]),o(()=>{let r;T(c,d.current.hsva)||e.equal(r=e.fromHsva(c),d.current.color)||(d.current={hsva:c,color:r},s(r))},[c,e,s]);const f=l(e=>{i(r=>Object.assign({},r,e))},[]);return[c,f]}const Y="undefined"!=typeof window?n:o;let R;const V=()=>R||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0),G=e=>{R=e},J=new Map,Q=e=>{Y(()=>{const r=e.current?e.current.ownerDocument:document;if(void 0!==r&&!J.has(r)){const e=r.createElement("style");e.innerHTML='.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>\')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}',J.set(r,e);const t=V();t&&e.setAttribute("nonce",t),r.head.appendChild(e)}},[])},U=t=>{let{className:o,colorModel:a,color:l=a.defaultColor,onChange:n}=t,u=c(t,["className","colorModel","color","onChange"]);const i=r(null);Q(i);const[d,f]=X(a,l,n),h=g(["react-colorful",o]);return e.createElement("div",s({},u,{ref:i,className:h}),e.createElement(S,{hsva:d,onChange:f}),e.createElement(A,{hue:d.h,onChange:f,className:"react-colorful__last-control"}))},W={defaultColor:"000",toHsva:x,fromHsva:({h:e,s:r,v:t})=>w({h:e,s:r,v:t,a:1}),equal:P},Z=r=>e.createElement(U,s({},r,{colorModel:W})),ee=({className:r,hsva:t,onChange:o})=>{const a={backgroundImage:`linear-gradient(90deg, ${q(Object.assign({},t,{a:0}))}, ${q(Object.assign({},t,{a:1}))})`},l=g(["react-colorful__alpha",r]),n=b(100*t.a);return e.createElement("div",{className:l},e.createElement("div",{className:"react-colorful__alpha-gradient",style:a}),e.createElement(m,{onMove:e=>{o({a:e.left})},onKey:e=>{o({a:i(t.a+e.left)})},"aria-label":"Alpha","aria-valuetext":`${n}%`,"aria-valuenow":n,"aria-valuemin":"0","aria-valuemax":"100"},e.createElement(p,{className:"react-colorful__alpha-pointer",left:t.a,color:q(t)})))},re=t=>{let{className:o,colorModel:a,color:l=a.defaultColor,onChange:n}=t,u=c(t,["className","colorModel","color","onChange"]);const i=r(null);Q(i);const[d,f]=X(a,l,n),h=g(["react-colorful",o]);return e.createElement("div",s({},u,{ref:i,className:h}),e.createElement(S,{hsva:d,onChange:f}),e.createElement(A,{hue:d.h,onChange:f}),e.createElement(ee,{hsva:d,onChange:f,className:"react-colorful__last-control"}))},te={defaultColor:"0001",toHsva:x,fromHsva:w,equal:P},oe=r=>e.createElement(re,s({},r,{colorModel:te})),ae={defaultColor:{h:0,s:0,l:0,a:1},toHsva:N,fromHsva:$,equal:T},le=r=>e.createElement(re,s({},r,{colorModel:ae})),ne={defaultColor:"hsla(0, 0%, 0%, 1)",toHsva:H,fromHsva:q,equal:F},se=r=>e.createElement(re,s({},r,{colorModel:ne})),ce={defaultColor:{h:0,s:0,l:0},toHsva:({h:e,s:r,l:t})=>N({h:e,s:r,l:t,a:1}),fromHsva:e=>(({h:e,s:r,l:t})=>({h:e,s:r,l:t}))($(e)),equal:T},ue=r=>e.createElement(U,s({},r,{colorModel:ce})),ie={defaultColor:"hsl(0, 0%, 0%)",toHsva:M,fromHsva:y,equal:F},de=r=>e.createElement(U,s({},r,{colorModel:ie})),fe={defaultColor:{h:0,s:0,v:0,a:1},toHsva:e=>e,fromHsva:L,equal:T},he=r=>e.createElement(re,s({},r,{colorModel:fe})),ve={defaultColor:"hsva(0, 0%, 0%, 1)",toHsva:I,fromHsva:e=>{const{h:r,s:t,v:o,a}=L(e);return`hsva(${r}, ${t}%, ${o}%, ${a})`},equal:F},me=r=>e.createElement(re,s({},r,{colorModel:ve})),ge={defaultColor:{h:0,s:0,v:0},toHsva:({h:e,s:r,v:t})=>({h:e,s:r,v:t,a:1}),fromHsva:e=>{const{h:r,s:t,v:o}=L(e);return{h:r,s:t,v:o}},equal:T},pe=r=>e.createElement(U,s({},r,{colorModel:ge})),be={defaultColor:"hsv(0, 0%, 0%)",toHsva:O,fromHsva:e=>{const{h:r,s:t,v:o}=L(e);return`hsv(${r}, ${t}%, ${o}%)`},equal:F},_e=r=>e.createElement(U,s({},r,{colorModel:be})),xe={defaultColor:{r:0,g:0,b:0,a:1},toHsva:K,fromHsva:k,equal:T},Ce=r=>e.createElement(re,s({},r,{colorModel:xe})),Ee={defaultColor:"rgba(0, 0, 0, 1)",toHsva:j,fromHsva:e=>{const{r,g:t,b:o,a}=k(e);return`rgba(${r}, ${t}, ${o}, ${a})`},equal:F},He=r=>e.createElement(re,s({},r,{colorModel:Ee})),Me={defaultColor:{r:0,g:0,b:0},toHsva:({r:e,g:r,b:t})=>K({r:e,g:r,b:t,a:1}),fromHsva:e=>(({r:e,g:r,b:t})=>({r:e,g:r,b:t}))(k(e)),equal:T},Ne=r=>e.createElement(U,s({},r,{colorModel:Me})),we={defaultColor:"rgb(0, 0, 0)",toHsva:z,fromHsva:e=>{const{r,g:t,b:o}=k(e);return`rgb(${r}, ${t}, ${o})`},equal:F},$e=r=>e.createElement(U,s({},r,{colorModel:we})),ye=/^#?([0-9A-F]{3,8})$/i,qe=r=>{const{color:t="",onChange:n,onBlur:i,escape:d,validate:f,format:h,process:v}=r,m=c(r,["color","onChange","onBlur","escape","validate","format","process"]),[g,p]=a(()=>d(t)),b=u(n),_=u(i),x=l(e=>{const r=d(e.target.value);p(r),f(r)&&b(v?v(r):r)},[d,v,f,b]),C=l(e=>{f(e.target.value)||p(d(t)),_(e)},[t,d,f,_]);return o(()=>{p(d(t))},[t,d]),e.createElement("input",s({},m,{value:h?h(g):g,spellCheck:"false",onChange:x,onBlur:C}))},ke=e=>"#"+e,Ie=r=>{const{prefixed:t,alpha:o}=r,a=c(r,["prefixed","alpha"]),n=l(e=>e.replace(/([^0-9A-F]+)/gi,"").substring(0,o?8:6),[o]),u=l(e=>((e,r)=>{const t=ye.exec(e),o=t?t[1].length:0;return 3===o||6===o||!!r&&4===o||!!r&&8===o})(e,o),[o]);return e.createElement(qe,s({},a,{escape:n,format:t?ke:void 0,process:ke,validate:u}))};export{oe as HexAlphaColorPicker,Ie as HexColorInput,Z as HexColorPicker,ue as HslColorPicker,de as HslStringColorPicker,le as HslaColorPicker,se as HslaStringColorPicker,pe as HsvColorPicker,_e as HsvStringColorPicker,he as HsvaColorPicker,me as HsvaStringColorPicker,Ne as RgbColorPicker,$e as RgbStringColorPicker,Ce as RgbaColorPicker,He as RgbaStringColorPicker,G as setNonce};
//# sourceMappingURL=index.esmodule.js.map
