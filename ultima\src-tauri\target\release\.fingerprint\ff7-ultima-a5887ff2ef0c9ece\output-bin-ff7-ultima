{"$message_type": "diagnostic", "message": "crate-level attribute should be in the root module", "code": {"code": "unused_attributes", "explanation": null}, "level": "warning", "spans": [{"file_name": "src\\commands.rs", "byte_start": 35, "byte_end": 64, "line_start": 1, "line_end": 1, "column_start": 36, "column_end": 65, "is_primary": true, "text": [{"text": "#![cfg_attr(not(debug_assertions), windows_subsystem = \"windows\")]", "highlight_start": 36, "highlight_end": 65}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "`#[warn(unused_attributes)]` on by default", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: crate-level attribute should be in the root module\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands.rs:1:36\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#![cfg_attr(not(debug_assertions), windows_subsystem = \"windows\")]\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_attributes)]` on by default\u001b[0m\n\n"}